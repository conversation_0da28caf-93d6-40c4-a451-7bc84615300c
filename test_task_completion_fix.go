package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
)

func main() {
	// Initialize database connection (you'll need to set up your config)
	// This is just a test script to verify the fix works
	
	ctx := context.Background()
	
	// Create factory
	factory := activity_cashback.NewTaskCompletionRepositoryFactory()
	
	// Test data
	userID := uuid.New()
	taskID := uuid.New()
	pointsAwarded := 100
	verificationData := map[string]interface{}{
		"test": "data",
		"timestamp": time.Now(),
	}
	
	// Test Daily Task Completion
	fmt.Println("Testing Daily Task Completion...")
	err := testDailyTaskCompletion(ctx, factory, userID, taskID, pointsAwarded, verificationData)
	if err != nil {
		log.Printf("Daily task completion test failed: %v", err)
	} else {
		fmt.Println("✅ Daily task completion test passed!")
	}
	
	// Test One-Time Task Completion
	fmt.Println("\nTesting One-Time Task Completion...")
	err = testOneTimeTaskCompletion(ctx, factory, userID, taskID, pointsAwarded, verificationData)
	if err != nil {
		log.Printf("One-time task completion test failed: %v", err)
	} else {
		fmt.Println("✅ One-time task completion test passed!")
	}
	
	// Test Unlimited Task Completion
	fmt.Println("\nTesting Unlimited Task Completion...")
	err = testUnlimitedTaskCompletion(ctx, factory, userID, taskID, pointsAwarded, verificationData)
	if err != nil {
		log.Printf("Unlimited task completion test failed: %v", err)
	} else {
		fmt.Println("✅ Unlimited task completion test passed!")
	}
	
	// Test Progressive Task Completion
	fmt.Println("\nTesting Progressive Task Completion...")
	err = testProgressiveTaskCompletion(ctx, factory, userID, taskID, pointsAwarded, verificationData)
	if err != nil {
		log.Printf("Progressive task completion test failed: %v", err)
	} else {
		fmt.Println("✅ Progressive task completion test passed!")
	}
	
	// Test Manual Task Completion
	fmt.Println("\nTesting Manual Task Completion...")
	err = testManualTaskCompletion(ctx, factory, userID, taskID, pointsAwarded, verificationData)
	if err != nil {
		log.Printf("Manual task completion test failed: %v", err)
	} else {
		fmt.Println("✅ Manual task completion test passed!")
	}
}

func testDailyTaskCompletion(ctx context.Context, factory *activity_cashback.TaskCompletionRepositoryFactory, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	// Create a mock daily task
	task := &model.ActivityTask{
		ID:        taskID,
		Name:      "Test Daily Task",
		Frequency: model.FrequencyDaily,
		Points:    pointsAwarded,
		IsActive:  true,
	}
	
	// Mock the task repository to return our test task
	// In a real test, you'd use a proper mock or test database
	
	// Test the completion creation
	now := time.Now()
	completion := &model.DailyTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		PointsAwarded:  pointsAwarded,
		CompletionDate: now.Truncate(24 * time.Hour),
		CompletionTime: now,
	}
	completion.SetVerificationData("auto", "system", verificationData)
	
	// Verify that CompletionDate and CompletionTime are set
	if completion.CompletionDate.IsZero() {
		return fmt.Errorf("CompletionDate is not set")
	}
	if completion.CompletionTime.IsZero() {
		return fmt.Errorf("CompletionTime is not set")
	}
	
	fmt.Printf("  CompletionDate: %v\n", completion.CompletionDate)
	fmt.Printf("  CompletionTime: %v\n", completion.CompletionTime)
	
	return nil
}

func testOneTimeTaskCompletion(ctx context.Context, factory *activity_cashback.TaskCompletionRepositoryFactory, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	now := time.Now()
	completion := &model.OneTimeTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		PointsAwarded:  pointsAwarded,
		CompletionDate: now,
	}
	completion.SetVerificationData("auto", "system", verificationData)
	
	if completion.CompletionDate.IsZero() {
		return fmt.Errorf("CompletionDate is not set")
	}
	
	fmt.Printf("  CompletionDate: %v\n", completion.CompletionDate)
	return nil
}

func testUnlimitedTaskCompletion(ctx context.Context, factory *activity_cashback.TaskCompletionRepositoryFactory, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	now := time.Now()
	completion := &model.UnlimitedTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		SequenceNumber: 1, // This would be calculated in the real implementation
		PointsAwarded:  pointsAwarded,
		CompletionDate: now,
	}
	completion.SetVerificationData("auto", "system", verificationData)
	
	if completion.CompletionDate.IsZero() {
		return fmt.Errorf("CompletionDate is not set")
	}
	if completion.SequenceNumber == 0 {
		return fmt.Errorf("SequenceNumber is not set")
	}
	
	fmt.Printf("  CompletionDate: %v\n", completion.CompletionDate)
	fmt.Printf("  SequenceNumber: %d\n", completion.SequenceNumber)
	return nil
}

func testProgressiveTaskCompletion(ctx context.Context, factory *activity_cashback.TaskCompletionRepositoryFactory, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	now := time.Now()
	completion := &model.ProgressiveTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		LevelCompleted: 1,
		TotalProgress:  1,
		PointsAwarded:  pointsAwarded,
		CompletionDate: now,
	}
	completion.SetVerificationData("auto", "system", verificationData)
	
	if completion.CompletionDate.IsZero() {
		return fmt.Errorf("CompletionDate is not set")
	}
	
	fmt.Printf("  CompletionDate: %v\n", completion.CompletionDate)
	fmt.Printf("  LevelCompleted: %d\n", completion.LevelCompleted)
	return nil
}

func testManualTaskCompletion(ctx context.Context, factory *activity_cashback.TaskCompletionRepositoryFactory, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	now := time.Now()
	completion := &model.ManualTaskCompletion{
		UserID:         userID,
		TaskID:         taskID,
		PointsAwarded:  pointsAwarded,
		CompletionDate: now,
		Status:         model.ManualTaskStatusPending,
	}
	completion.SetVerificationData("auto", "system", verificationData)
	
	if completion.CompletionDate.IsZero() {
		return fmt.Errorf("CompletionDate is not set")
	}
	
	fmt.Printf("  CompletionDate: %v\n", completion.CompletionDate)
	fmt.Printf("  Status: %s\n", completion.Status)
	return nil
}
