# Task Completion Tables Design

## Overview

This document describes the design for splitting the `task_completion_history` table into separate tables for each task frequency type to improve performance and optimize storage.

## Current Issues

The current `task_completion_history` table stores all completion records regardless of task frequency, leading to:
- Heavy read/write operations when dealing with mixed frequency types
- Suboptimal indexing for different access patterns
- Inefficient queries for frequency-specific operations

## New Table Design

### 1. Daily Task Completions (`daily_task_completions`)

**Purpose**: Store completions for tasks with DAILY frequency
**Characteristics**: High volume, time-based partitioning, automatic cleanup

```sql
CREATE TABLE daily_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date DATE NOT NULL DEFAULT CURRENT_DATE,
    completion_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_daily_completions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_daily_completions_task FOREIGN KEY (task_id) REFERENCES activity_tasks(id)
) PARTITION BY RANGE (completion_date);

-- Indexes optimized for daily tasks
CREATE INDEX idx_daily_completions_user_date ON daily_task_completions (user_id, completion_date);
CREATE INDEX idx_daily_completions_task_date ON daily_task_completions (task_id, completion_date);
CREATE INDEX idx_daily_completions_date ON daily_task_completions (completion_date);

-- Partitions (example for current month)
CREATE TABLE daily_task_completions_2025_01 PARTITION OF daily_task_completions
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. One-Time Task Completions (`one_time_task_completions`)

**Purpose**: Store completions for tasks with ONE_TIME frequency
**Characteristics**: Low volume, permanent storage, unique constraints

```sql
CREATE TABLE one_time_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_onetime_completions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_onetime_completions_task FOREIGN KEY (task_id) REFERENCES activity_tasks(id),
    
    -- Unique constraint to ensure one completion per user per task
    CONSTRAINT uk_onetime_user_task UNIQUE (user_id, task_id)
);

-- Indexes optimized for one-time tasks
CREATE INDEX idx_onetime_completions_user ON one_time_task_completions (user_id);
CREATE INDEX idx_onetime_completions_task ON one_time_task_completions (task_id);
CREATE INDEX idx_onetime_completions_date ON one_time_task_completions (completion_date);
```

### 3. Unlimited Task Completions (`unlimited_task_completions`)

**Purpose**: Store completions for tasks with UNLIMITED frequency
**Characteristics**: Very high volume, sequence tracking, bulk operations

```sql
CREATE TABLE unlimited_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    sequence_number BIGINT NOT NULL, -- Track completion order
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_unlimited_completions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_unlimited_completions_task FOREIGN KEY (task_id) REFERENCES activity_tasks(id)
);

-- Indexes optimized for unlimited tasks
CREATE INDEX idx_unlimited_completions_user_task ON unlimited_task_completions (user_id, task_id);
CREATE INDEX idx_unlimited_completions_sequence ON unlimited_task_completions (user_id, task_id, sequence_number);
CREATE INDEX idx_unlimited_completions_date ON unlimited_task_completions (completion_date);

-- Sequence for tracking completion order
CREATE SEQUENCE unlimited_completion_sequence;
```

### 4. Progressive Task Completions (`progressive_task_completions`)

**Purpose**: Store completions for tasks with PROGRESSIVE frequency
**Characteristics**: Level-based progression, milestone tracking

```sql
CREATE TABLE progressive_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    level_completed INTEGER NOT NULL, -- Which level/milestone was completed
    total_progress INTEGER NOT NULL DEFAULT 0, -- Cumulative progress
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    milestone_data JSONB, -- Store milestone-specific data
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_progressive_completions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_progressive_completions_task FOREIGN KEY (task_id) REFERENCES activity_tasks(id),
    
    -- Unique constraint to ensure one completion per user per task per level
    CONSTRAINT uk_progressive_user_task_level UNIQUE (user_id, task_id, level_completed)
);

-- Indexes optimized for progressive tasks
CREATE INDEX idx_progressive_completions_user_task ON progressive_task_completions (user_id, task_id);
CREATE INDEX idx_progressive_completions_level ON progressive_task_completions (user_id, task_id, level_completed);
CREATE INDEX idx_progressive_completions_date ON progressive_task_completions (completion_date);
```

### 5. Manual Task Completions (`manual_task_completions`)

**Purpose**: Store completions for tasks with MANUAL frequency
**Characteristics**: Admin-triggered, approval workflow, audit trail

```sql
CREATE TABLE manual_task_completions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    task_id UUID NOT NULL,
    points_awarded INTEGER NOT NULL DEFAULT 0,
    completion_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_data JSONB,
    approved_by UUID, -- Admin who approved the completion
    approval_date TIMESTAMP,
    approval_notes TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    CONSTRAINT fk_manual_completions_user FOREIGN KEY (user_id) REFERENCES users(id),
    CONSTRAINT fk_manual_completions_task FOREIGN KEY (task_id) REFERENCES activity_tasks(id),
    CONSTRAINT fk_manual_completions_approver FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- Indexes optimized for manual tasks
CREATE INDEX idx_manual_completions_user ON manual_task_completions (user_id);
CREATE INDEX idx_manual_completions_task ON manual_task_completions (task_id);
CREATE INDEX idx_manual_completions_status ON manual_task_completions (status);
CREATE INDEX idx_manual_completions_approver ON manual_task_completions (approved_by);
CREATE INDEX idx_manual_completions_date ON manual_task_completions (completion_date);
```

## Performance Benefits

### 1. Optimized Indexing
- Each table has indexes specific to its access patterns
- Daily tasks: date-based partitioning and indexes
- One-time tasks: unique constraints prevent duplicates
- Unlimited tasks: sequence tracking for order
- Progressive tasks: level-based indexing
- Manual tasks: status and approval workflow indexes

### 2. Reduced Table Size
- Each table contains only relevant data for its frequency type
- Smaller tables = faster queries and better cache utilization

### 3. Specialized Operations
- Daily tasks: automatic partition pruning for date ranges
- One-time tasks: fast duplicate checking with unique constraints
- Unlimited tasks: efficient sequence-based ordering
- Progressive tasks: milestone tracking without scanning all records
- Manual tasks: approval workflow without affecting other types

### 4. Maintenance Benefits
- Daily tasks: automatic partition management and cleanup
- Separate backup/archival strategies per table
- Independent performance tuning per frequency type

## Migration Strategy

1. Create new tables with optimized schemas
2. Create factory pattern for repository selection
3. Update application code to use factory pattern
4. Migrate existing data based on task frequency
5. Update documentation and tests
6. Drop old table after verification

## Backward Compatibility

- Factory pattern maintains existing interface
- GraphQL API responses remain unchanged
- Existing queries continue to work through abstraction layer
