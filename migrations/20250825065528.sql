-- Create "activity_cashback_claims" table
CREATE TABLE "public"."activity_cashback_claims" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "claim_type" character varying(50) NOT NULL,
  "total_amount_usd" numeric(38,6) NOT NULL,
  "total_amount_sol" numeric(38,6) NOT NULL,
  "transaction_hash" character varying(255) NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING',
  "claimed_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "processed_at" timestamptz NULL,
  "metadata" jsonb NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_cashback_claims_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_activity_cashback_claims_user_id" to table: "activity_cashback_claims"
CREATE INDEX "idx_activity_cashback_claims_user_id" ON "public"."activity_cashback_claims" ("user_id");
-- Create "task_categories" table
CREATE TABLE "public"."task_categories" (
  "id" bigserial NOT NULL,
  "name" character varying(50) NOT NULL,
  "display_name" character varying(100) NOT NULL,
  "description" text NULL,
  "icon" character varying(255) NULL,
  "sort_order" bigint NULL DEFAULT 0,
  "is_active" boolean NULL DEFAULT true,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_task_categories_name" UNIQUE ("name")
);
-- Create "activity_tasks" table
CREATE TABLE "public"."activity_tasks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "category_id" bigint NOT NULL,
  "name" character varying(100) NOT NULL,
  "description" text NULL,
  "task_type" character varying(20) NOT NULL,
  "frequency" character varying(20) NOT NULL,
  "points" bigint NOT NULL DEFAULT 0,
  "max_completions" bigint NULL,
  "reset_period" character varying(20) NULL,
  "conditions" jsonb NULL,
  "action_target" character varying(255) NULL,
  "verification_method" character varying(50) NULL,
  "external_link" character varying(500) NULL,
  "is_active" boolean NULL DEFAULT true,
  "start_date" timestamptz NULL,
  "end_date" timestamptz NULL,
  "sort_order" bigint NULL DEFAULT 0,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "created_by" uuid NULL,
  "updated_by" uuid NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_task_categories_tasks" FOREIGN KEY ("category_id") REFERENCES "public"."task_categories" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create "task_completion_history" table
CREATE TABLE "public"."task_completion_history" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "points_awarded" bigint NOT NULL,
  "completion_date" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_task_completion_history_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_task_completion_history_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_task_completion_history_task_id" to table: "task_completion_history"
CREATE INDEX "idx_task_completion_history_task_id" ON "public"."task_completion_history" ("task_id");
-- Create index "idx_task_completion_history_user_id" to table: "task_completion_history"
CREATE INDEX "idx_task_completion_history_user_id" ON "public"."task_completion_history" ("user_id");
-- Create "user_task_progress" table
CREATE TABLE "public"."user_task_progress" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'NOT_STARTED',
  "progress_value" bigint NULL DEFAULT 0,
  "target_value" bigint NULL,
  "completion_count" bigint NULL DEFAULT 0,
  "points_earned" bigint NULL DEFAULT 0,
  "last_completed_at" timestamptz NULL,
  "last_reset_at" timestamptz NULL,
  "streak_count" bigint NULL DEFAULT 0,
  "metadata" jsonb NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_tasks_user_progress" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_user_task_progress_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_task_progress_task_id" to table: "user_task_progress"
CREATE INDEX "idx_user_task_progress_task_id" ON "public"."user_task_progress" ("task_id");
-- Create index "idx_user_task_progress_user_id" to table: "user_task_progress"
CREATE INDEX "idx_user_task_progress_user_id" ON "public"."user_task_progress" ("user_id");
-- Create "tier_benefits" table
CREATE TABLE "public"."tier_benefits" (
  "id" bigserial NOT NULL,
  "tier_level" bigint NOT NULL,
  "tier_name" character varying(50) NOT NULL,
  "min_points" bigint NOT NULL,
  "cashback_percentage" numeric(5,4) NOT NULL,
  "benefits_description" text NULL,
  "tier_color" character varying(7) NULL,
  "tier_icon" character varying(255) NULL,
  "is_active" boolean NULL DEFAULT true,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_tier_benefits_tier_level" UNIQUE ("tier_level")
);
-- Create "user_tier_info" table
CREATE TABLE "public"."user_tier_info" (
  "user_id" uuid NOT NULL,
  "current_tier" bigint NOT NULL DEFAULT 1,
  "total_points" bigint NOT NULL DEFAULT 0,
  "points_this_month" bigint NOT NULL DEFAULT 0,
  "trading_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "active_days_this_month" bigint NULL DEFAULT 0,
  "cumulative_cashback_usd" numeric(38,6) NULL DEFAULT 0,
  "claimable_cashback_usd" numeric(38,6) NULL DEFAULT 0,
  "claimed_cashback_usd" numeric(38,6) NULL DEFAULT 0,
  "last_activity_date" timestamptz NULL,
  "tier_upgraded_at" timestamptz NULL,
  "monthly_reset_at" timestamptz NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("user_id"),
  CONSTRAINT "fk_tier_benefits_users" FOREIGN KEY ("current_tier") REFERENCES "public"."tier_benefits" ("tier_level") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_user_tier_info_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
