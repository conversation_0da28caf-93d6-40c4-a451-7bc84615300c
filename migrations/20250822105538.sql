-- Create "activity_cashback_claims" table
CREATE TABLE "public"."activity_cashback_claims" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "claim_type" character varying(50) NOT NULL,
  "total_amount" numeric(38,18) NOT NULL,
  "total_amount_usd" numeric(38,18) NOT NULL,
  "currency" character varying(10) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING',
  "claimed_at" timestamptz NOT NULL,
  "processed_at" timestamptz NULL,
  "transaction_hash" character varying(255) NULL,
  "block_number" bigint NULL,
  "reward_ids" text NULL,
  "description" text NULL,
  "metadata" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_cashback_claims_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- <PERSON>reate index "idx_activity_cashback_claims_user_id" to table: "activity_cashback_claims"
CREATE INDEX "idx_activity_cashback_claims_user_id" ON "public"."activity_cashback_claims" ("user_id");
-- Create "activity_rewards" table
CREATE TABLE "public"."activity_rewards" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "type" character varying(20) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING',
  "amount" numeric(38,18) NOT NULL DEFAULT 0,
  "amount_usd" numeric(38,18) NULL DEFAULT 0,
  "currency" character varying(10) NULL DEFAULT 'POINTS',
  "source_type" character varying(50) NOT NULL,
  "source_id" uuid NULL,
  "source_name" character varying(255) NULL,
  "earned_at" timestamptz NOT NULL,
  "claimed_at" timestamptz NULL,
  "expires_at" timestamptz NULL,
  "description" text NULL,
  "metadata" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_rewards_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_activity_rewards_user_id" to table: "activity_rewards"
CREATE INDEX "idx_activity_rewards_user_id" ON "public"."activity_rewards" ("user_id");
-- Create "activity_tiers" table
CREATE TABLE "public"."activity_tiers" (
  "id" bigserial NOT NULL,
  "name" character varying(50) NOT NULL,
  "level" bigint NOT NULL,
  "min_points" bigint NOT NULL,
  "max_points" bigint NULL,
  "color" character varying(20) NOT NULL,
  "icon" character varying(100) NULL,
  "description" text NULL,
  "cashback_percentage" numeric(10,6) NOT NULL DEFAULT 0,
  "benefits" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_activity_tiers_level" UNIQUE ("level")
);
-- Create "activity_tier_upgrades" table
CREATE TABLE "public"."activity_tier_upgrades" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "from_tier_id" bigint NOT NULL,
  "to_tier_id" bigint NOT NULL,
  "points_at_upgrade" bigint NOT NULL,
  "trigger_type" character varying(50) NOT NULL,
  "trigger_data" text NULL,
  "created_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_tier_upgrades_from_tier" FOREIGN KEY ("from_tier_id") REFERENCES "public"."activity_tiers" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_activity_tier_upgrades_to_tier" FOREIGN KEY ("to_tier_id") REFERENCES "public"."activity_tiers" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_activity_tier_upgrades_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_activity_tier_upgrades_user_id" to table: "activity_tier_upgrades"
CREATE INDEX "idx_activity_tier_upgrades_user_id" ON "public"."activity_tier_upgrades" ("user_id");
-- Create "activity_tasks" table
CREATE TABLE "public"."activity_tasks" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "name" character varying(255) NOT NULL,
  "description" text NULL,
  "type" character varying(20) NOT NULL,
  "category" character varying(20) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'ACTIVE',
  "required_count" bigint NOT NULL DEFAULT 1,
  "required_volume" numeric(38,18) NULL DEFAULT 0,
  "min_volume" numeric(38,18) NULL DEFAULT 0,
  "time_window_hours" bigint NULL,
  "valid_from" timestamptz NULL,
  "valid_until" timestamptz NULL,
  "point_reward" bigint NOT NULL DEFAULT 0,
  "cashback_reward" numeric(38,18) NULL DEFAULT 0,
  "config" text NULL,
  "metadata" text NULL,
  "icon" character varying(100) NULL,
  "color" character varying(20) NULL,
  "priority" bigint NOT NULL DEFAULT 0,
  "is_highlight" boolean NOT NULL DEFAULT false,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id")
);
-- Create "task_completions" table
CREATE TABLE "public"."task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "completed_at" timestamptz NOT NULL,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "cashback_amount" numeric(38,18) NULL DEFAULT 0,
  "trigger_type" character varying(50) NOT NULL,
  "trigger_source" character varying(100) NULL,
  "reference_id" uuid NULL,
  "reference_type" character varying(50) NULL,
  "completion_data" text NULL,
  "created_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_task_completions_task_id" to table: "task_completions"
CREATE INDEX "idx_task_completions_task_id" ON "public"."task_completions" ("task_id");
-- Create index "idx_task_completions_user_id" to table: "task_completions"
CREATE INDEX "idx_task_completions_user_id" ON "public"."task_completions" ("user_id");
-- Create "user_activity_levels" table
CREATE TABLE "public"."user_activity_levels" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "activity_tier_id" bigint NOT NULL DEFAULT 1,
  "total_points" bigint NOT NULL DEFAULT 0,
  "current_points" bigint NOT NULL DEFAULT 0,
  "total_trading_volume" numeric(38,18) NOT NULL DEFAULT 0,
  "monthly_trading_volume" numeric(38,18) NOT NULL DEFAULT 0,
  "monthly_active_days" bigint NOT NULL DEFAULT 0,
  "total_cashback_earned" numeric(38,18) NOT NULL DEFAULT 0,
  "last_tier_upgrade" timestamptz NULL,
  "tier_upgrade_count" bigint NOT NULL DEFAULT 0,
  "previous_tier_id" bigint NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_user_activity_levels_user_id" UNIQUE ("user_id"),
  CONSTRAINT "fk_user_activity_levels_activity_tier" FOREIGN KEY ("activity_tier_id") REFERENCES "public"."activity_tiers" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_user_activity_levels_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_activity_levels_user_id" to table: "user_activity_levels"
CREATE INDEX "idx_user_activity_levels_user_id" ON "public"."user_activity_levels" ("user_id");
-- Create "user_activity_stats" table
CREATE TABLE "public"."user_activity_stats" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "daily_tasks_completed" bigint NOT NULL DEFAULT 0,
  "daily_points_earned" bigint NOT NULL DEFAULT 0,
  "daily_cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "daily_trading_volume" numeric(38,18) NULL DEFAULT 0,
  "daily_last_reset" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "weekly_tasks_completed" bigint NOT NULL DEFAULT 0,
  "weekly_points_earned" bigint NOT NULL DEFAULT 0,
  "weekly_cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "weekly_trading_volume" numeric(38,18) NULL DEFAULT 0,
  "weekly_last_reset" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "monthly_tasks_completed" bigint NOT NULL DEFAULT 0,
  "monthly_points_earned" bigint NOT NULL DEFAULT 0,
  "monthly_cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "monthly_trading_volume" numeric(38,18) NULL DEFAULT 0,
  "monthly_active_days" bigint NOT NULL DEFAULT 0,
  "monthly_last_reset" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "total_tasks_completed" bigint NOT NULL DEFAULT 0,
  "total_points_earned" bigint NOT NULL DEFAULT 0,
  "total_cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "total_trading_volume" numeric(38,18) NULL DEFAULT 0,
  "total_active_days" bigint NOT NULL DEFAULT 0,
  "current_login_streak" bigint NOT NULL DEFAULT 0,
  "best_login_streak" bigint NOT NULL DEFAULT 0,
  "current_trading_streak" bigint NOT NULL DEFAULT 0,
  "best_trading_streak" bigint NOT NULL DEFAULT 0,
  "last_activity_at" timestamptz NULL,
  "last_trading_at" timestamptz NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "uni_user_activity_stats_user_id" UNIQUE ("user_id"),
  CONSTRAINT "fk_user_activity_stats_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_activity_stats_user_id" to table: "user_activity_stats"
CREATE INDEX "idx_user_activity_stats_user_id" ON "public"."user_activity_stats" ("user_id");
-- Create "user_daily_activities" table
CREATE TABLE "public"."user_daily_activities" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "date" date NOT NULL,
  "has_login" boolean NOT NULL DEFAULT false,
  "has_trading" boolean NOT NULL DEFAULT false,
  "has_task_completion" boolean NOT NULL DEFAULT false,
  "has_social_activity" boolean NOT NULL DEFAULT false,
  "tasks_completed" bigint NOT NULL DEFAULT 0,
  "points_earned" bigint NOT NULL DEFAULT 0,
  "cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "trading_volume" numeric(38,18) NULL DEFAULT 0,
  "transaction_count" bigint NOT NULL DEFAULT 0,
  "first_activity_at" timestamptz NULL,
  "last_activity_at" timestamptz NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_user_daily_activities_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_daily_activities_date" to table: "user_daily_activities"
CREATE INDEX "idx_user_daily_activities_date" ON "public"."user_daily_activities" ("date");
-- Create index "idx_user_daily_activities_user_id" to table: "user_daily_activities"
CREATE INDEX "idx_user_daily_activities_user_id" ON "public"."user_daily_activities" ("user_id");
-- Create "user_point_transactions" table
CREATE TABLE "public"."user_point_transactions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "points" bigint NOT NULL,
  "type" character varying(50) NOT NULL,
  "source" character varying(100) NOT NULL,
  "description" text NULL,
  "reference_id" uuid NULL,
  "reference_type" character varying(50) NULL,
  "metadata" text NULL,
  "created_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_user_activity_levels_point_history" FOREIGN KEY ("user_id") REFERENCES "public"."user_activity_levels" ("user_id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_user_point_transactions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_point_transactions_user_id" to table: "user_point_transactions"
CREATE INDEX "idx_user_point_transactions_user_id" ON "public"."user_point_transactions" ("user_id");
-- Create "user_task_progress" table
CREATE TABLE "public"."user_task_progress" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'NOT_STARTED',
  "current_count" bigint NOT NULL DEFAULT 0,
  "current_volume" numeric(38,18) NULL DEFAULT 0,
  "completed_count" bigint NOT NULL DEFAULT 0,
  "current_streak" bigint NOT NULL DEFAULT 0,
  "best_streak" bigint NOT NULL DEFAULT 0,
  "last_streak_at" timestamptz NULL,
  "started_at" timestamptz NULL,
  "completed_at" timestamptz NULL,
  "claimed_at" timestamptz NULL,
  "expires_at" timestamptz NULL,
  "last_update_at" timestamptz NULL,
  "period_start" timestamptz NULL,
  "period_end" timestamptz NULL,
  "points_earned" bigint NOT NULL DEFAULT 0,
  "cashback_earned" numeric(38,18) NULL DEFAULT 0,
  "total_reward_usd" numeric(38,18) NULL DEFAULT 0,
  "metadata" text NULL,
  "created_at" timestamptz NULL,
  "updated_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_activity_tasks_user_progress" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_user_task_progress_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_user_task_progress_task_id" to table: "user_task_progress"
CREATE INDEX "idx_user_task_progress_task_id" ON "public"."user_task_progress" ("task_id");
-- Create index "idx_user_task_progress_user_id" to table: "user_task_progress"
CREATE INDEX "idx_user_task_progress_user_id" ON "public"."user_task_progress" ("user_id");
