-- Create "daily_task_completions" table
CREATE TABLE "public"."daily_task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "completion_date" date NOT NULL DEFAULT CURRENT_DATE,
  "completion_time" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_daily_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_daily_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- <PERSON><PERSON> index "idx_daily_completions_date" to table: "daily_task_completions"
CREATE INDEX "idx_daily_completions_date" ON "public"."daily_task_completions" ("completion_date");
-- Create index "idx_daily_completions_task_date" to table: "daily_task_completions"
CREATE INDEX "idx_daily_completions_task_date" ON "public"."daily_task_completions" ("task_id");
-- Create index "idx_daily_completions_user_date" to table: "daily_task_completions"
CREATE INDEX "idx_daily_completions_user_date" ON "public"."daily_task_completions" ("user_id");
-- Create "manual_task_completions" table
CREATE TABLE "public"."manual_task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "completion_date" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "approved_by" uuid NULL,
  "approval_date" timestamptz NULL,
  "approval_notes" text NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING',
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_manual_task_completions_approver" FOREIGN KEY ("approved_by") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_manual_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_manual_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_manual_completions_approver" to table: "manual_task_completions"
CREATE INDEX "idx_manual_completions_approver" ON "public"."manual_task_completions" ("approved_by");
-- Create index "idx_manual_completions_date" to table: "manual_task_completions"
CREATE INDEX "idx_manual_completions_date" ON "public"."manual_task_completions" ("completion_date");
-- Create index "idx_manual_completions_status" to table: "manual_task_completions"
CREATE INDEX "idx_manual_completions_status" ON "public"."manual_task_completions" ("status");
-- Create index "idx_manual_completions_task" to table: "manual_task_completions"
CREATE INDEX "idx_manual_completions_task" ON "public"."manual_task_completions" ("task_id");
-- Create index "idx_manual_completions_user" to table: "manual_task_completions"
CREATE INDEX "idx_manual_completions_user" ON "public"."manual_task_completions" ("user_id");
-- Create "one_time_task_completions" table
CREATE TABLE "public"."one_time_task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "completion_date" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_one_time_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_one_time_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_onetime_completions_date" to table: "one_time_task_completions"
CREATE INDEX "idx_onetime_completions_date" ON "public"."one_time_task_completions" ("completion_date");
-- Create index "idx_onetime_completions_task" to table: "one_time_task_completions"
CREATE INDEX "idx_onetime_completions_task" ON "public"."one_time_task_completions" ("task_id");
-- Create index "idx_onetime_completions_user" to table: "one_time_task_completions"
CREATE INDEX "idx_onetime_completions_user" ON "public"."one_time_task_completions" ("user_id");
-- Create index "uk_onetime_user_task" to table: "one_time_task_completions"
CREATE UNIQUE INDEX "uk_onetime_user_task" ON "public"."one_time_task_completions" ("user_id", "task_id");
-- Create "progressive_task_completions" table
CREATE TABLE "public"."progressive_task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "level_completed" bigint NOT NULL,
  "total_progress" bigint NOT NULL DEFAULT 0,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "completion_date" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "milestone_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_progressive_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_progressive_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_progressive_completions_date" to table: "progressive_task_completions"
CREATE INDEX "idx_progressive_completions_date" ON "public"."progressive_task_completions" ("completion_date");
-- Create index "idx_progressive_completions_level" to table: "progressive_task_completions"
CREATE INDEX "idx_progressive_completions_level" ON "public"."progressive_task_completions" ("level_completed");
-- Create index "idx_progressive_completions_user_task" to table: "progressive_task_completions"
CREATE INDEX "idx_progressive_completions_user_task" ON "public"."progressive_task_completions" ("user_id", "task_id");
-- Create index "uk_progressive_user_task_level" to table: "progressive_task_completions"
CREATE UNIQUE INDEX "uk_progressive_user_task_level" ON "public"."progressive_task_completions" ("user_id", "task_id", "level_completed");
-- Create "unlimited_task_completions" table
CREATE TABLE "public"."unlimited_task_completions" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "task_id" uuid NOT NULL,
  "sequence_number" bigint NOT NULL,
  "points_awarded" bigint NOT NULL DEFAULT 0,
  "completion_date" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "verification_data" jsonb NULL,
  "ip_address" inet NULL,
  "user_agent" text NULL,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_unlimited_task_completions_task" FOREIGN KEY ("task_id") REFERENCES "public"."activity_tasks" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_unlimited_task_completions_user" FOREIGN KEY ("user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_unlimited_completions_date" to table: "unlimited_task_completions"
CREATE INDEX "idx_unlimited_completions_date" ON "public"."unlimited_task_completions" ("completion_date");
-- Create index "idx_unlimited_completions_sequence" to table: "unlimited_task_completions"
CREATE INDEX "idx_unlimited_completions_sequence" ON "public"."unlimited_task_completions" ("sequence_number");
-- Create index "idx_unlimited_completions_user_task" to table: "unlimited_task_completions"
CREATE INDEX "idx_unlimited_completions_user_task" ON "public"."unlimited_task_completions" ("user_id", "task_id");
