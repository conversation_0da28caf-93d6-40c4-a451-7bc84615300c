package resolvers

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	infinite_agent_repository "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/infinite_agent"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/infinite_agent"
)

type InfiniteAgentConfigResolver struct {
	s infinite_agent.InfiniteAgentConfigI
}

func NewInfiniteAgentConfigResolver() *InfiniteAgentConfigResolver {
	repo := infinite_agent_repository.NewInfiniteAgentConfigRepository(global.GVA_DB)
	service := infinite_agent.NewInfiniteAgentConfigService(repo)
	return &InfiniteAgentConfigResolver{
		s: service,
	}
}

// InfiniteAgentConfigs is the resolver for the infiniteAgentConfigs field.
func (r *InfiniteAgentConfigResolver) InfiniteAgentConfigs(ctx context.Context) (*gql_model.InfiniteAgentConfigsResponse, error) {
	configs, err := r.s.GetAll(ctx)
	if err != nil {
		return &gql_model.InfiniteAgentConfigsResponse{
			InfiniteAgentConfigs: []*gql_model.InfiniteAgentConfig{},
			Success:              false,
			Message:              fmt.Sprintf("Failed to get infinite agent configs: %v", err),
		}, nil
	}

	var gqlConfigs []*gql_model.InfiniteAgentConfig
	for _, config := range configs {
		gqlConfig := ModelInfiniteAgentConfigToGQL(config)
		gqlConfigs = append(gqlConfigs, gqlConfig)
	}

	return &gql_model.InfiniteAgentConfigsResponse{
		InfiniteAgentConfigs: gqlConfigs,
		Success:              true,
		Message:              "Infinite agent configs retrieved successfully",
	}, nil
}

// InfiniteAgentConfig is the resolver for the infiniteAgentConfig field.
func (r *InfiniteAgentConfigResolver) InfiniteAgentConfig(ctx context.Context, id string) (*gql_model.InfiniteAgentConfigResponse, error) {
	configID, err := uuid.Parse(id)
	if err != nil {
		return &gql_model.InfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             "Invalid ID format",
		}, nil
	}

	config, err := r.s.GetByID(ctx, configID)
	if err != nil {
		return &gql_model.InfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             fmt.Sprintf("Failed to get infinite agent config: %v", err),
		}, nil
	}

	return &gql_model.InfiniteAgentConfigResponse{
		InfiniteAgentConfig: ModelInfiniteAgentConfigToGQL(config),
		Success:             true,
		Message:             "Infinite agent config retrieved successfully",
	}, nil
}

// CreateInfiniteAgentConfig is the resolver for the createInfiniteAgentConfig field.
func (r *InfiniteAgentConfigResolver) CreateInfiniteAgentConfig(ctx context.Context, input gql_model.CreateInfiniteAgentConfigInput) (*gql_model.CreateInfiniteAgentConfigResponse, error) {
	userID, err := uuid.Parse(input.UserID)
	if err != nil {
		return &gql_model.CreateInfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             "Invalid user ID format",
		}, nil
	}

	commissionRateN := decimal.NewFromFloat(input.CommissionRateN)

	config, err := r.s.Create(ctx, userID, commissionRateN, string(input.Status))
	if err != nil {
		return &gql_model.CreateInfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             fmt.Sprintf("Failed to create infinite agent config: %v", err),
		}, nil
	}

	return &gql_model.CreateInfiniteAgentConfigResponse{
		InfiniteAgentConfig: ModelInfiniteAgentConfigToGQL(config),
		Success:             true,
		Message:             "Infinite agent config created successfully",
	}, nil
}

// UpdateInfiniteAgentConfig is the resolver for the updateInfiniteAgentConfig field.
func (r *InfiniteAgentConfigResolver) UpdateInfiniteAgentConfig(ctx context.Context, input gql_model.UpdateInfiniteAgentConfigInput) (*gql_model.UpdateInfiniteAgentConfigResponse, error) {
	configID, err := uuid.Parse(input.ID)
	if err != nil {
		return &gql_model.UpdateInfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             "Invalid config ID format",
		}, nil
	}

	if input.UserID == nil && input.CommissionRateN == nil && input.Status == nil {
		return &gql_model.UpdateInfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             "No fields to update",
		}, nil
	}

	var userID uuid.UUID
	if input.UserID != nil {
		userID, err = uuid.Parse(*input.UserID)
		if err != nil {
			return &gql_model.UpdateInfiniteAgentConfigResponse{
				InfiniteAgentConfig: nil,
				Success:             false,
				Message:             "Invalid user ID format",
			}, nil
		}
		fmt.Println("Parsed userID:", userID)
	} else {
		fmt.Println("input.UserID is nil, userID will be uuid.Nil")
	}

	var commissionRateN decimal.Decimal
	var status string
	if input.CommissionRateN != nil {
		commissionRateN = decimal.NewFromFloat(*input.CommissionRateN)
	}
	if input.Status != nil {
		status = string(*input.Status)
	}

	config, err := r.s.Update(ctx, configID, userID, commissionRateN, status)
	if err != nil {
		return &gql_model.UpdateInfiniteAgentConfigResponse{
			InfiniteAgentConfig: nil,
			Success:             false,
			Message:             fmt.Sprintf("Failed to update infinite agent config: %v", err),
		}, nil
	}

	return &gql_model.UpdateInfiniteAgentConfigResponse{
		InfiniteAgentConfig: ModelInfiniteAgentConfigToGQL(config),
		Success:             true,
		Message:             "Infinite agent config updated successfully",
	}, nil
}

// DeleteInfiniteAgentConfig is the resolver for the deleteInfiniteAgentConfig field.
func (r *InfiniteAgentConfigResolver) DeleteInfiniteAgentConfig(ctx context.Context, input gql_model.DeleteInfiniteAgentConfigInput) (*gql_model.DeleteInfiniteAgentConfigResponse, error) {
	configID, err := uuid.Parse(input.ID)
	if err != nil {
		return &gql_model.DeleteInfiniteAgentConfigResponse{
			Success: false,
			Message: "Invalid config ID format",
		}, nil
	}

	err = r.s.Delete(ctx, configID)
	if err != nil {
		return &gql_model.DeleteInfiniteAgentConfigResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to delete infinite agent config: %v", err),
		}, nil
	}

	return &gql_model.DeleteInfiniteAgentConfigResponse{
		Success: true,
		Message: "Infinite agent config deleted successfully",
	}, nil
}

// ModelInfiniteAgentConfigToGQL converts model.InfiniteAgentConfig to gql_model.InfiniteAgentConfig
func ModelInfiniteAgentConfigToGQL(config *model.InfiniteAgentConfig) *gql_model.InfiniteAgentConfig {
	if config == nil {
		return nil
	}

	gqlConfig := &gql_model.InfiniteAgentConfig{
		ID:              config.ID.String(),
		UserID:          config.UserID.String(),
		CommissionRateN: config.CommissionRateN.InexactFloat64(),
		Status:          config.Status,
		CreatedAt:       config.CreatedAt,
		UpdatedAt:       config.UpdatedAt,
	}

	if config.User.ID != uuid.Nil {
		gqlConfig.User = ModelUserToGQL(&config.User)
	}

	return gqlConfig
}

func ModelUserToGQL(user *model.User) *gql_model.User {
	if user == nil {
		return nil
	}

	return &gql_model.User{
		ID:    user.ID.String(),
		Email: user.Email,
	}
}
