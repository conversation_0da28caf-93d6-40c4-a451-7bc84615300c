# Infinite Agent Config management schema

input CreateInfiniteAgentConfigInput {
  userID: ID!
  commissionRateN: Float!
  status: StatusType!
}

input UpdateInfiniteAgentConfigInput {
  id: ID!
  userID: ID
  commissionRateN: Float
  status: StatusType
}

enum StatusType {
  ACTIVE
  INACTIVE
}

input DeleteInfiniteAgentConfigInput {
  id: ID!
}

type InfiniteAgentConfig {
  id: ID!
  userID: ID!
  commissionRateN: Float!
  Status: String!
  createdAt: Time!
  updatedAt: Time!
  user: User
}

type CreateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

type UpdateInfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}

type DeleteInfiniteAgentConfigResponse {
  success: Boolean!
  message: String!
}

type InfiniteAgentConfigsResponse {
  infiniteAgentConfigs: [InfiniteAgentConfig!]!
  success: Boolean!
  message: String!
}

type InfiniteAgentConfigResponse {
  infiniteAgentConfig: InfiniteAgentConfig
  success: Boolean!
  message: String!
}
