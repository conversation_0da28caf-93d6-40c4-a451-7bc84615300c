package graphql

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.78

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/graphql/resolvers"
)

// CreateUserWithReferral is the resolver for the createUserWithReferral field.
func (r *mutationResolver) CreateUserWithReferral(ctx context.Context, input gql_model.CreateUserWithReferralInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserWithReferral(ctx, input)
}

// CreateUserInvitationCode is the resolver for the createUserInvitationCode field.
func (r *mutationResolver) CreateUserInvitationCode(ctx context.Context, input gql_model.CreateUserInvitationCodeInput) (*gql_model.CreateUserResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.CreateUserInvitationCode(ctx, input)
}

// UpdateLevelCommission is the resolver for the updateLevelCommission field.
func (r *mutationResolver) UpdateLevelCommission(ctx context.Context, input gql_model.UpdateLevelCommissionInput) (*gql_model.UpdateLevelCommissionResponse, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.UpdateLevelCommission(ctx, input)
}

// ClaimAgentReferral is the resolver for the claimAgentReferral field.
func (r *mutationResolver) ClaimAgentReferral(ctx context.Context, input gql_model.ClaimAgentReferralInput) (*gql_model.ClaimResultResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.ClaimAgentReferral(ctx, input)
}

// CreateInfiniteAgentConfig is the resolver for the createInfiniteAgentConfig field.
func (r *mutationResolver) CreateInfiniteAgentConfig(ctx context.Context, input gql_model.CreateInfiniteAgentConfigInput) (*gql_model.CreateInfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.CreateInfiniteAgentConfig(ctx, input)
}

// UpdateInfiniteAgentConfig is the resolver for the updateInfiniteAgentConfig field.
func (r *mutationResolver) UpdateInfiniteAgentConfig(ctx context.Context, input gql_model.UpdateInfiniteAgentConfigInput) (*gql_model.UpdateInfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.UpdateInfiniteAgentConfig(ctx, input)
}

// DeleteInfiniteAgentConfig is the resolver for the deleteInfiniteAgentConfig field.
func (r *mutationResolver) DeleteInfiniteAgentConfig(ctx context.Context, input gql_model.DeleteInfiniteAgentConfigInput) (*gql_model.DeleteInfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.DeleteInfiniteAgentConfig(ctx, input)
}

// ReferralSnapshot is the resolver for the referralSnapshot field.
func (r *queryResolver) ReferralSnapshot(ctx context.Context) (*gql_model.ReferralSnapshot, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.ReferralSnapshot(ctx)
}

// AgentLevels is the resolver for the agentLevels field.
func (r *queryResolver) AgentLevels(ctx context.Context) ([]*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevels(ctx)
}

// AgentLevel is the resolver for the agentLevel field.
func (r *queryResolver) AgentLevel(ctx context.Context, id int) (*gql_model.AgentLevel, error) {
	levelResolver := resolvers.NewLevelResolver()
	return levelResolver.AgentLevel(ctx, id)
}

// TransactionData is the resolver for the transactionData field.
func (r *queryResolver) TransactionData(ctx context.Context, input gql_model.TransactionDataInput) (*gql_model.TransactionDataResponse, error) {
	transactionResolver := resolvers.NewTransactionResolver()
	return transactionResolver.TransactionData(ctx, input)
}

// DataOverview is the resolver for the dataOverview field.
func (r *queryResolver) DataOverview(ctx context.Context, input gql_model.DataOverviewInput) (*gql_model.DataOverviewWithSummary, error) {
	return r.DataOverviewService.DataOverview(ctx, input)
}

// UserInvitationData is the resolver for the userInvitationData field.
func (r *queryResolver) UserInvitationData(ctx context.Context) (*gql_model.UserInvitationDataResponse, error) {
	invitationResolver := resolvers.NewInvitationResolver()
	return invitationResolver.UserInvitationData(ctx)
}

// RewardData is the resolver for the rewardData field.
func (r *queryResolver) RewardData(ctx context.Context) (*gql_model.RewardDataResponse, error) {
	return r.RewardService.RewardData(ctx)
}

// InvitationRecords is the resolver for the invitationRecords field.
func (r *queryResolver) InvitationRecords(ctx context.Context) (*gql_model.InvitationRecordResponse, error) {
	return r.RewardService.InvitationRecords(ctx)
}

// GetClaimReward is the resolver for the getClaimReward field.
func (r *queryResolver) GetClaimReward(ctx context.Context) (*gql_model.ClaimRewardResponse, error) {
	claimResolver := resolvers.NewClaimResolver()
	return claimResolver.GetClaimReward(ctx)
}

// InfiniteAgentConfigs is the resolver for the infiniteAgentConfigs field.
func (r *queryResolver) InfiniteAgentConfigs(ctx context.Context) (*gql_model.InfiniteAgentConfigsResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.InfiniteAgentConfigs(ctx)
}

// InfiniteAgentConfig is the resolver for the infiniteAgentConfig field.
func (r *queryResolver) InfiniteAgentConfig(ctx context.Context, id string) (*gql_model.InfiniteAgentConfigResponse, error) {
	infiniteAgentConfigResolver := resolvers.NewInfiniteAgentConfigResolver()
	return infiniteAgentConfigResolver.InfiniteAgentConfig(ctx, id)
}

// Mutation returns MutationResolver implementation.
func (r *Resolver) Mutation() MutationResolver { return &mutationResolver{r} }

// Query returns QueryResolver implementation.
func (r *Resolver) Query() QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
