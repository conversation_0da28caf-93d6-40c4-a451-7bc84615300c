package infinite_agent

import (
	"context"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type InfiniteAgentConfigI interface {
	Create(ctx context.Context, config *model.InfiniteAgentConfig) error
	Update(ctx context.Context, config *model.InfiniteAgentConfig) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error)
}

type InfiniteAgentConfigRepository struct{}

func NewInfiniteAgentConfigRepository(db *gorm.DB) InfiniteAgentConfigI {
	return &InfiniteAgentConfigRepository{}
}

func (r *InfiniteAgentConfigRepository) Create(ctx context.Context, config *model.InfiniteAgentConfig) error {
	return global.GVA_DB.WithContext(ctx).Create(config).Error
}

func (r *InfiniteAgentConfigRepository) Update(ctx context.Context, config *model.InfiniteAgentConfig) error {
	return global.GVA_DB.Debug().WithContext(ctx).Model(config).Updates(map[string]interface{}{
		"user_id":                            config.UserID,
		"commission_rate_n":                  config.CommissionRateN,
		"total_net_fee_usd":                  config.TotalNetFeeUSD,
		"total_standard_commission_paid_usd": config.TotalStandardCommissionPaidUSD,
		"final_commission_amount_usd":        config.FinalCommissionAmountUSD,
		"status":                             config.Status,
		"updated_at":                         config.UpdatedAt,
	}).Error
}

func (r *InfiniteAgentConfigRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return global.GVA_DB.Debug().WithContext(ctx).Where("id = ?", id).Delete(&model.InfiniteAgentConfig{}).Error
}

func (r *InfiniteAgentConfigRepository) GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	var config model.InfiniteAgentConfig
	err := global.GVA_DB.WithContext(ctx).Preload("User").Where("id = ?", id).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (r *InfiniteAgentConfigRepository) GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error) {
	var config model.InfiniteAgentConfig
	err := global.GVA_DB.WithContext(ctx).Preload("User").Where("user_id = ?", userID).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (r *InfiniteAgentConfigRepository) GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error) {
	var configs []*model.InfiniteAgentConfig
	err := global.GVA_DB.WithContext(ctx).Preload("User").Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}
