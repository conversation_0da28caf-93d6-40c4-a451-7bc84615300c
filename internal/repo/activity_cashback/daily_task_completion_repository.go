package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
)

// DailyTaskCompletionRepository implements DailyTaskCompletionRepositoryInterface
type DailyTaskCompletionRepository struct {
	db *gorm.DB
}

// NewDailyTaskCompletionRepository creates a new DailyTaskCompletionRepository
func NewDailyTaskCompletionRepository() DailyTaskCompletionRepositoryInterface {
	return &DailyTaskCompletionRepository{
		db: global.GVA_DB,
	}
}

// Create creates a new daily task completion record
func (r *DailyTaskCompletionRepository) Create(ctx context.Context, completion *model.DailyTaskCompletion) error {
	// Ensure partition exists for the completion date
	if err := r.CreatePartitionIfNotExists(ctx, completion.CompletionDate); err != nil {
		return fmt.Errorf("failed to create partition: %w", err)
	}
	
	return r.db.WithContext(ctx).Create(completion).Error
}

// GetByUserID retrieves daily task completions by user ID with pagination
func (r *DailyTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error) {
	var completions []model.DailyTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ?", userID).
		Order("completion_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByTaskID retrieves daily task completions by task ID with pagination
func (r *DailyTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error) {
	var completions []model.DailyTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ?", taskID).
		Order("completion_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndTask retrieves daily task completions by user and task with pagination
func (r *DailyTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.DailyTaskCompletion, error) {
	var completions []model.DailyTaskCompletion
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND task_id = ?", userID, taskID).
		Order("completion_time DESC").
		Limit(limit).
		Offset(offset).
		Find(&completions).Error
	return completions, err
}

// GetByUserAndDate retrieves daily task completions by user and specific date
func (r *DailyTaskCompletionRepository) GetByUserAndDate(ctx context.Context, userID uuid.UUID, date time.Time) ([]model.DailyTaskCompletion, error) {
	var completions []model.DailyTaskCompletion
	targetDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	
	err := r.db.WithContext(ctx).
		Preload("Task").
		Preload("Task.Category").
		Where("user_id = ? AND completion_date = ?", userID, targetDate).
		Order("completion_time DESC").
		Find(&completions).Error
	return completions, err
}

// GetByTaskAndDate retrieves daily task completions by task and specific date
func (r *DailyTaskCompletionRepository) GetByTaskAndDate(ctx context.Context, taskID uuid.UUID, date time.Time) ([]model.DailyTaskCompletion, error) {
	var completions []model.DailyTaskCompletion
	targetDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
	
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("task_id = ? AND completion_date = ?", taskID, targetDate).
		Order("completion_time DESC").
		Find(&completions).Error
	return completions, err
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range
func (r *DailyTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	var results []struct {
		TaskName string `json:"task_name"`
		Count    int    `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.DailyTaskCompletion{}).
		Select("activity_tasks.name as task_name, COUNT(*) as count").
		Joins("JOIN activity_tasks ON daily_task_completions.task_id = activity_tasks.id").
		Where("daily_task_completions.user_id = ? AND daily_task_completions.completion_date BETWEEN ? AND ?", userID, startDate, endDate).
		Group("activity_tasks.name").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[string]int)
	for _, result := range results {
		stats[result.TaskName] = result.Count
	}

	return stats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *DailyTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.DailyTaskCompletion{}).
		Where("task_id = ? AND completion_date BETWEEN ? AND ?", taskID, startDate, endDate).
		Count(&count).Error
	return int(count), err
}

// GetDailyCompletionStats retrieves daily completion statistics for all tasks on a specific date
func (r *DailyTaskCompletionRepository) GetDailyCompletionStats(ctx context.Context, date time.Time) (map[uuid.UUID]int, error) {
	targetDate := time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())

	var results []struct {
		TaskID uuid.UUID `json:"task_id"`
		Count  int       `json:"count"`
	}

	err := r.db.WithContext(ctx).
		Model(&model.DailyTaskCompletion{}).
		Select("task_id, COUNT(*) as count").
		Where("completion_date = ?", targetDate).
		Group("task_id").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	stats := make(map[uuid.UUID]int)
	for _, result := range results {
		stats[result.TaskID] = result.Count
	}

	return stats, nil
}

// HasUserCompletedTaskToday checks if user has completed a specific task today
func (r *DailyTaskCompletionRepository) HasUserCompletedTaskToday(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	today := time.Now().Truncate(24 * time.Hour)
	
	var count int64
	err := r.db.WithContext(ctx).
		Model(&model.DailyTaskCompletion{}).
		Where("user_id = ? AND task_id = ? AND completion_date = ?", userID, taskID, today).
		Count(&count).Error
	
	return count > 0, err
}

// CreatePartitionIfNotExists creates a partition for the given date if it doesn't exist
func (r *DailyTaskCompletionRepository) CreatePartitionIfNotExists(ctx context.Context, date time.Time) error {
	year := date.Year()
	month := int(date.Month())
	
	partitionName := fmt.Sprintf("daily_task_completions_%d_%02d", year, month)
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.UTC)
	endDate := startDate.AddDate(0, 1, 0)
	
	// Check if partition exists
	var exists bool
	err := r.db.WithContext(ctx).Raw(`
		SELECT EXISTS (
			SELECT 1 FROM pg_tables 
			WHERE tablename = ?
		)
	`, partitionName).Scan(&exists).Error
	
	if err != nil {
		return err
	}
	
	if !exists {
		// Create partition
		sql := fmt.Sprintf(`
			CREATE TABLE %s PARTITION OF daily_task_completions
			FOR VALUES FROM ('%s') TO ('%s')
		`, partitionName, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
		
		err = r.db.WithContext(ctx).Exec(sql).Error
		if err != nil {
			return fmt.Errorf("failed to create partition %s: %w", partitionName, err)
		}
	}
	
	return nil
}
