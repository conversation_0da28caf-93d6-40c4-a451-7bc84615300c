package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TaskCompletionRepositoryFactory provides a unified interface for all task completion repositories
type TaskCompletionRepositoryFactory struct {
	dailyRepo       DailyTaskCompletionRepositoryInterface
	oneTimeRepo     OneTimeTaskCompletionRepositoryInterface
	unlimitedRepo   UnlimitedTaskCompletionRepositoryInterface
	progressiveRepo ProgressiveTaskCompletionRepositoryInterface
	manualRepo      ManualTaskCompletionRepositoryInterface
	taskRepo        ActivityTaskRepositoryInterface
}

// NewTaskCompletionRepositoryFactory creates a new TaskCompletionRepositoryFactory
func NewTaskCompletionRepositoryFactory() *TaskCompletionRepositoryFactory {
	return &TaskCompletionRepositoryFactory{
		dailyRepo:       NewDailyTaskCompletionRepository(),
		oneTimeRepo:     NewOneTimeTaskCompletionRepository(),
		unlimitedRepo:   NewUnlimitedTaskCompletionRepository(),
		progressiveRepo: NewProgressiveTaskCompletionRepository(),
		manualRepo:      NewManualTaskCompletionRepository(),
		taskRepo:        NewActivityTaskRepository(),
	}
}

// TaskCompletionRepositoryInterface defines a unified interface for task completion operations
type TaskCompletionRepositoryInterface interface {
	Create(ctx context.Context, completion model.TaskCompletionInterface) error
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error)
	GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error)
	GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error)
	GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error)
	GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error)
	GetDailyCompletionStats(ctx context.Context, date time.Time) (map[uuid.UUID]int, error)
}

// UnifiedTaskCompletionRepository implements TaskCompletionRepositoryInterface using the factory pattern
type UnifiedTaskCompletionRepository struct {
	factory *TaskCompletionRepositoryFactory
}

// NewUnifiedTaskCompletionRepository creates a new UnifiedTaskCompletionRepository
func NewUnifiedTaskCompletionRepository() TaskCompletionRepositoryInterface {
	return &UnifiedTaskCompletionRepository{
		factory: NewTaskCompletionRepositoryFactory(),
	}
}

// Create creates a task completion record using the appropriate repository based on task frequency
func (r *UnifiedTaskCompletionRepository) Create(ctx context.Context, completion model.TaskCompletionInterface) error {
	// Get task to determine frequency
	task, err := r.factory.taskRepo.GetByID(ctx, completion.GetTaskID())
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Direct type assertion and creation based on frequency
	switch task.Frequency {
	case model.FrequencyDaily:
		if dailyCompletion, ok := completion.(*model.DailyTaskCompletion); ok {
			return r.factory.dailyRepo.Create(ctx, dailyCompletion)
		}
		return fmt.Errorf("completion type mismatch for daily task")

	case model.FrequencyOneTime:
		if oneTimeCompletion, ok := completion.(*model.OneTimeTaskCompletion); ok {
			return r.factory.oneTimeRepo.Create(ctx, oneTimeCompletion)
		}
		return fmt.Errorf("completion type mismatch for one-time task")

	case model.FrequencyUnlimited:
		if unlimitedCompletion, ok := completion.(*model.UnlimitedTaskCompletion); ok {
			return r.factory.unlimitedRepo.Create(ctx, unlimitedCompletion)
		}
		return fmt.Errorf("completion type mismatch for unlimited task")

	case model.FrequencyProgressive:
		if progressiveCompletion, ok := completion.(*model.ProgressiveTaskCompletion); ok {
			return r.factory.progressiveRepo.Create(ctx, progressiveCompletion)
		}
		return fmt.Errorf("completion type mismatch for progressive task")

	case model.FrequencyManual:
		if manualCompletion, ok := completion.(*model.ManualTaskCompletion); ok {
			return r.factory.manualRepo.Create(ctx, manualCompletion)
		}
		return fmt.Errorf("completion type mismatch for manual task")

	default:
		return fmt.Errorf("unsupported task frequency: %s", task.Frequency)
	}
}

// GetByUserID retrieves task completions by user ID from all repositories
func (r *UnifiedTaskCompletionRepository) GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error) {
	var allCompletions []model.TaskCompletionInterface

	// Get from daily repository
	dailyCompletions, err := r.factory.dailyRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get daily completions: %w", err)
	}
	for _, completion := range dailyCompletions {
		allCompletions = append(allCompletions, &completion)
	}

	// Get from one-time repository
	oneTimeCompletions, err := r.factory.oneTimeRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get one-time completions: %w", err)
	}
	for _, completion := range oneTimeCompletions {
		allCompletions = append(allCompletions, &completion)
	}

	// Get from unlimited repository
	unlimitedCompletions, err := r.factory.unlimitedRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get unlimited completions: %w", err)
	}
	for _, completion := range unlimitedCompletions {
		allCompletions = append(allCompletions, &completion)
	}

	// Get from progressive repository
	progressiveCompletions, err := r.factory.progressiveRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get progressive completions: %w", err)
	}
	for _, completion := range progressiveCompletions {
		allCompletions = append(allCompletions, &completion)
	}

	// Get from manual repository
	manualCompletions, err := r.factory.manualRepo.GetByUserID(ctx, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get manual completions: %w", err)
	}
	for _, completion := range manualCompletions {
		allCompletions = append(allCompletions, &completion)
	}

	// Sort by completion date (most recent first)
	// Note: This is a simplified implementation. In production, you might want to implement
	// a more sophisticated sorting/pagination mechanism across multiple repositories

	return allCompletions, nil
}

// GetByTaskID retrieves task completions by task ID using the appropriate repository
func (r *UnifiedTaskCompletionRepository) GetByTaskID(ctx context.Context, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error) {
	// Get task to determine frequency
	task, err := r.factory.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	var completions []model.TaskCompletionInterface

	switch task.Frequency {
	case model.FrequencyDaily:
		dailyCompletions, err := r.factory.dailyRepo.GetByTaskID(ctx, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range dailyCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyOneTime:
		oneTimeCompletions, err := r.factory.oneTimeRepo.GetByTaskID(ctx, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range oneTimeCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyUnlimited:
		unlimitedCompletions, err := r.factory.unlimitedRepo.GetByTaskID(ctx, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range unlimitedCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyProgressive:
		progressiveCompletions, err := r.factory.progressiveRepo.GetByTaskID(ctx, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range progressiveCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyManual:
		manualCompletions, err := r.factory.manualRepo.GetByTaskID(ctx, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range manualCompletions {
			completions = append(completions, &completion)
		}

	default:
		return nil, fmt.Errorf("unsupported task frequency: %s", task.Frequency)
	}

	return completions, nil
}

// GetByUserAndTask retrieves task completions by user and task using the appropriate repository
func (r *UnifiedTaskCompletionRepository) GetByUserAndTask(ctx context.Context, userID, taskID uuid.UUID, limit, offset int) ([]model.TaskCompletionInterface, error) {
	// Get task to determine frequency
	task, err := r.factory.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	var completions []model.TaskCompletionInterface

	switch task.Frequency {
	case model.FrequencyDaily:
		dailyCompletions, err := r.factory.dailyRepo.GetByUserAndTask(ctx, userID, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range dailyCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyOneTime:
		oneTimeCompletion, err := r.factory.oneTimeRepo.GetByUserAndTask(ctx, userID, taskID)
		if err != nil {
			return nil, err
		}
		if oneTimeCompletion != nil {
			completions = append(completions, oneTimeCompletion)
		}

	case model.FrequencyUnlimited:
		unlimitedCompletions, err := r.factory.unlimitedRepo.GetByUserAndTask(ctx, userID, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range unlimitedCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyProgressive:
		progressiveCompletions, err := r.factory.progressiveRepo.GetByUserAndTask(ctx, userID, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range progressiveCompletions {
			completions = append(completions, &completion)
		}

	case model.FrequencyManual:
		manualCompletions, err := r.factory.manualRepo.GetByUserAndTask(ctx, userID, taskID, limit, offset)
		if err != nil {
			return nil, err
		}
		for _, completion := range manualCompletions {
			completions = append(completions, &completion)
		}

	default:
		return nil, fmt.Errorf("unsupported task frequency: %s", task.Frequency)
	}

	return completions, nil
}

// GetUserCompletionStats retrieves completion statistics for a user within a date range from all repositories
func (r *UnifiedTaskCompletionRepository) GetUserCompletionStats(ctx context.Context, userID uuid.UUID, startDate, endDate time.Time) (map[string]int, error) {
	allStats := make(map[string]int)

	// Get stats from daily repository
	dailyStats, err := r.factory.dailyRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get daily completion stats: %w", err)
	}
	for taskName, count := range dailyStats {
		allStats[taskName] += count
	}

	// Get stats from one-time repository
	oneTimeStats, err := r.factory.oneTimeRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get one-time completion stats: %w", err)
	}
	for taskName, count := range oneTimeStats {
		allStats[taskName] += count
	}

	// Get stats from unlimited repository
	unlimitedStats, err := r.factory.unlimitedRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get unlimited completion stats: %w", err)
	}
	for taskName, count := range unlimitedStats {
		allStats[taskName] += count
	}

	// Get stats from progressive repository
	progressiveStats, err := r.factory.progressiveRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get progressive completion stats: %w", err)
	}
	for taskName, count := range progressiveStats {
		allStats[taskName] += count
	}

	// Get stats from manual repository
	manualStats, err := r.factory.manualRepo.GetUserCompletionStats(ctx, userID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to get manual completion stats: %w", err)
	}
	for taskName, count := range manualStats {
		allStats[taskName] += count
	}

	return allStats, nil
}

// GetTaskCompletionStats retrieves completion statistics for a specific task within a date range
func (r *UnifiedTaskCompletionRepository) GetTaskCompletionStats(ctx context.Context, taskID uuid.UUID, startDate, endDate time.Time) (int, error) {
	// Get task to determine frequency
	task, err := r.factory.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return 0, fmt.Errorf("failed to get task: %w", err)
	}

	switch task.Frequency {
	case model.FrequencyDaily:
		return r.factory.dailyRepo.GetTaskCompletionStats(ctx, taskID, startDate, endDate)
	case model.FrequencyOneTime:
		return r.factory.oneTimeRepo.GetTaskCompletionStats(ctx, taskID, startDate, endDate)
	case model.FrequencyUnlimited:
		return r.factory.unlimitedRepo.GetTaskCompletionStats(ctx, taskID, startDate, endDate)
	case model.FrequencyProgressive:
		return r.factory.progressiveRepo.GetTaskCompletionStats(ctx, taskID, startDate, endDate)
	case model.FrequencyManual:
		return r.factory.manualRepo.GetTaskCompletionStats(ctx, taskID, startDate, endDate)
	default:
		return 0, fmt.Errorf("unsupported task frequency: %s", task.Frequency)
	}
}

// GetRepositoryByFrequency returns the appropriate repository for a given task frequency
func (f *TaskCompletionRepositoryFactory) GetRepositoryByFrequency(frequency model.TaskFrequency) interface{} {
	switch frequency {
	case model.FrequencyDaily:
		return f.dailyRepo
	case model.FrequencyOneTime:
		return f.oneTimeRepo
	case model.FrequencyUnlimited:
		return f.unlimitedRepo
	case model.FrequencyProgressive:
		return f.progressiveRepo
	case model.FrequencyManual:
		return f.manualRepo
	default:
		return nil
	}
}

// GetUnifiedRepository returns a unified repository interface
func (f *TaskCompletionRepositoryFactory) GetUnifiedRepository() TaskCompletionRepositoryInterface {
	return &UnifiedTaskCompletionRepository{
		factory: f,
	}
}

// CreateTaskCompletion creates a task completion using the appropriate repository based on task frequency
func (f *TaskCompletionRepositoryFactory) CreateTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, pointsAwarded int, verificationData map[string]interface{}) error {
	// Get task to determine frequency
	task, err := f.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	switch task.Frequency {
	case model.FrequencyDaily:
		completion := &model.DailyTaskCompletion{
			UserID:        userID,
			TaskID:        taskID,
			PointsAwarded: pointsAwarded,
		}
		completion.SetVerificationData("auto", "system", verificationData)
		return f.dailyRepo.Create(ctx, completion)

	case model.FrequencyOneTime:
		completion := &model.OneTimeTaskCompletion{
			UserID:        userID,
			TaskID:        taskID,
			PointsAwarded: pointsAwarded,
		}
		completion.SetVerificationData("auto", "system", verificationData)
		return f.oneTimeRepo.Create(ctx, completion)

	case model.FrequencyUnlimited:
		completion := &model.UnlimitedTaskCompletion{
			UserID:        userID,
			TaskID:        taskID,
			PointsAwarded: pointsAwarded,
		}
		completion.SetVerificationData("auto", "system", verificationData)
		return f.unlimitedRepo.Create(ctx, completion)

	case model.FrequencyProgressive:
		// For progressive tasks, we need additional parameters
		// This is a simplified implementation
		completion := &model.ProgressiveTaskCompletion{
			UserID:         userID,
			TaskID:         taskID,
			LevelCompleted: 1, // Default to level 1
			TotalProgress:  1, // Default to 1
			PointsAwarded:  pointsAwarded,
		}
		completion.SetVerificationData("auto", "system", verificationData)
		return f.progressiveRepo.Create(ctx, completion)

	case model.FrequencyManual:
		completion := &model.ManualTaskCompletion{
			UserID:        userID,
			TaskID:        taskID,
			PointsAwarded: pointsAwarded,
			Status:        model.ManualTaskStatusPending,
		}
		completion.SetVerificationData("auto", "system", verificationData)
		return f.manualRepo.Create(ctx, completion)

	default:
		return fmt.Errorf("unsupported task frequency: %s", task.Frequency)
	}
}

// GetDailyCompletionStats retrieves daily completion statistics for all tasks on a specific date
func (r *UnifiedTaskCompletionRepository) GetDailyCompletionStats(ctx context.Context, date time.Time) (map[uuid.UUID]int, error) {
	// Only daily tasks have meaningful daily completion stats
	return r.factory.dailyRepo.GetDailyCompletionStats(ctx, date)
}
