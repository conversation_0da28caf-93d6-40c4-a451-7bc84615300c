package service

import (
	"context"
	"errors"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
)

// MockLevelRepo is a mock implementation of repo.LevelRepo
type MockLevelRepo struct {
	mock.Mock
}

func (m *MockLevelRepo) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	args := m.Called(ctx)
	return args.Get(0).([]model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*model.AgentLevel), args.Error(1)
}

func (m *MockLevelRepo) UpdateAgentLevel(ctx context.Context, level *model.AgentLevel) error {
	args := m.Called(ctx, level)
	return args.Error(0)
}

func TestLevelService_GetAgentLevels(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name           string
		mockSetup      func(*MockLevelRepo)
		expectedLevels []model.AgentLevel
		expectedError  error
	}{
		{
			name: "successful retrieval of agent levels",
			mockSetup: func(mockRepo *MockLevelRepo) {
				levels := []model.AgentLevel{
					*fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
					*fixtures.CreateTestAgentLevelWithID(2, "Lv2"),
				}
				mockRepo.On("GetAgentLevels", mock.Anything).Return(levels, nil)
			},
			expectedLevels: []model.AgentLevel{
				*fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
				*fixtures.CreateTestAgentLevelWithID(2, "Lv2"),
			},
			expectedError: nil,
		},
		{
			name: "repository error",
			mockSetup: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel{}, errors.New("database error"))
			},
			expectedLevels: []model.AgentLevel{},
			expectedError:  errors.New("database error"),
		},
		{
			name: "empty result",
			mockSetup: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevels", mock.Anything).Return([]model.AgentLevel{}, nil)
			},
			expectedLevels: []model.AgentLevel{},
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockLevelRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := &LevelService{
				levelRepo: mockRepo,
			}

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := service.GetAgentLevels(ctx)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertEqual(tt.expectedError.Error(), err.Error())
			} else {
				helper.AssertNoError(err)
			}

			helper.AssertEqual(len(tt.expectedLevels), len(result))
			for i, expectedLevel := range tt.expectedLevels {
				if i < len(result) {
					helper.AssertEqual(expectedLevel.ID, result[i].ID)
					helper.AssertEqual(expectedLevel.Name, result[i].Name)
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLevelService_GetAgentLevelByID(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		levelID       uint
		mockSetup     func(*MockLevelRepo)
		expectedLevel *model.AgentLevel
		expectedError error
	}{
		{
			name:    "successful retrieval by ID",
			levelID: 1,
			mockSetup: func(mockRepo *MockLevelRepo) {
				level := fixtures.CreateTestAgentLevelWithID(1, "Lv1")
				mockRepo.On("GetAgentLevelByID", mock.Anything, uint(1)).Return(level, nil)
			},
			expectedLevel: fixtures.CreateTestAgentLevelWithID(1, "Lv1"),
			expectedError: nil,
		},
		{
			name:    "level not found",
			levelID: 999,
			mockSetup: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, uint(999)).Return((*model.AgentLevel)(nil), errors.New("level not found"))
			},
			expectedLevel: nil,
			expectedError: errors.New("level not found"),
		},
		{
			name:    "repository error",
			levelID: 1,
			mockSetup: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, uint(1)).Return((*model.AgentLevel)(nil), errors.New("database error"))
			},
			expectedLevel: nil,
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockLevelRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := &LevelService{
				levelRepo: mockRepo,
			}

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := service.GetAgentLevelByID(ctx, tt.levelID)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertEqual(tt.expectedError.Error(), err.Error())
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				if tt.expectedLevel != nil {
					helper.AssertEqual(tt.expectedLevel.ID, result.ID)
					helper.AssertEqual(tt.expectedLevel.Name, result.Name)
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}

func TestLevelService_UpdateLevelCommission(t *testing.T) {
	// Setup test configuration
	test.SetupTestConfig()
	defer test.CleanupTestConfig()

	fixtures := test.NewTestFixtures()
	helper := test.NewTestHelper(t)

	tests := []struct {
		name          string
		levelID       uint
		directRate    float64
		indirectRate  float64
		extendedRate  float64
		memeFeeRebate float64
		mockSetup     func(*MockLevelRepo)
		expectedLevel *model.AgentLevel
		expectedError error
	}{
		{
			name:          "successful update with valid rates",
			levelID:       1,
			directRate:    0.30,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.25,
			mockSetup: func(mockRepo *MockLevelRepo) {
				level := fixtures.CreateTestAgentLevelWithID(1, "Lv1")
				level.DirectCommissionRate = decimal.NewFromFloat(0.30)
				level.IndirectCommissionRate = decimal.NewFromFloat(0.05)
				level.ExtendedCommissionRate = decimal.NewFromFloat(0.025)
				level.MemeFeeRebate = decimal.NewFromFloat(0.25)

				mockRepo.On("GetAgentLevelByID", mock.Anything, uint(1)).Return(level, nil)
				mockRepo.On("UpdateAgentLevel", mock.Anything, mock.AnythingOfType("*model.AgentLevel")).Return(nil)
			},
			expectedLevel: func() *model.AgentLevel {
				level := fixtures.CreateTestAgentLevelWithID(1, "Lv1")
				level.DirectCommissionRate = decimal.NewFromFloat(0.30)
				level.IndirectCommissionRate = decimal.NewFromFloat(0.05)
				level.ExtendedCommissionRate = decimal.NewFromFloat(0.025)
				level.MemeFeeRebate = decimal.NewFromFloat(0.25)
				return level
			}(),
			expectedError: nil,
		},
		{
			name:          "invalid direct rate - negative",
			levelID:       1,
			directRate:    -0.1,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.25,
			mockSetup:     func(mockRepo *MockLevelRepo) {},
			expectedLevel: nil,
			expectedError: errors.New("direct commission rate must be between 0 and 1"),
		},
		{
			name:          "invalid direct rate - greater than 1",
			levelID:       1,
			directRate:    1.5,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.25,
			mockSetup:     func(mockRepo *MockLevelRepo) {},
			expectedLevel: nil,
			expectedError: errors.New("direct commission rate must be between 0 and 1"),
		},
		{
			name:          "invalid indirect rate - negative",
			levelID:       1,
			directRate:    0.30,
			indirectRate:  -0.1,
			extendedRate:  0.025,
			memeFeeRebate: 0.25,
			mockSetup:     func(mockRepo *MockLevelRepo) {},
			expectedLevel: nil,
			expectedError: errors.New("indirect commission rate must be between 0 and 1"),
		},
		{
			name:          "invalid extended rate - greater than 1",
			levelID:       1,
			directRate:    0.30,
			indirectRate:  0.05,
			extendedRate:  1.5,
			memeFeeRebate: 0.25,
			mockSetup:     func(mockRepo *MockLevelRepo) {},
			expectedLevel: nil,
			expectedError: errors.New("extended commission rate must be between 0 and 1"),
		},
		{
			name:          "invalid meme fee rebate - negative",
			levelID:       1,
			directRate:    0.30,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: -0.1,
			mockSetup:     func(mockRepo *MockLevelRepo) {},
			expectedLevel: nil,
			expectedError: errors.New("meme fee rebate must be between 0 and 1"),
		},
		{
			name:          "level not found",
			levelID:       999,
			directRate:    0.30,
			indirectRate:  0.05,
			extendedRate:  0.025,
			memeFeeRebate: 0.25,
			mockSetup: func(mockRepo *MockLevelRepo) {
				mockRepo.On("GetAgentLevelByID", mock.Anything, uint(999)).Return((*model.AgentLevel)(nil), errors.New("level not found"))
			},
			expectedLevel: nil,
			expectedError: errors.New("level not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock repository
			mockRepo := &MockLevelRepo{}
			tt.mockSetup(mockRepo)

			// Create service with mock repository
			service := &LevelService{
				levelRepo: mockRepo,
			}

			// Execute test
			ctx := helper.CreateTestContext()
			result, err := service.UpdateLevelCommission(ctx, tt.levelID, tt.directRate, tt.indirectRate, tt.extendedRate, tt.memeFeeRebate)

			// Verify results
			if tt.expectedError != nil {
				helper.AssertError(err)
				helper.AssertContains(err.Error(), tt.expectedError.Error())
				helper.AssertNil(result)
			} else {
				helper.AssertNoError(err)
				helper.AssertNotNil(result)
				if tt.expectedLevel != nil {
					helper.AssertEqual(tt.expectedLevel.ID, result.ID)
					helper.AssertEqual(tt.expectedLevel.Name, result.Name)
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}
