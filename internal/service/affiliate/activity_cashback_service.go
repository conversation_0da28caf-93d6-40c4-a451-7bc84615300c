package affiliate

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
)

// ActivityCashbackService handles activity transaction cashback calculations and commission distribution
type ActivityCashbackService struct {
	affiliateRepo        repo.AffiliateRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	levelRepo            repo.LevelRepo
	activityCashbackRepo repo.ActivityCashbackRepositoryInterface
}

// NewActivityCashbackService creates a new activity cashback service
func NewActivityCashbackService() *ActivityCashbackService {
	return &ActivityCashbackService{
		affiliateRepo:        repo.NewAffiliateRepository(),
		userRepo:             transaction.NewUserRepository(),
		levelRepo:            repo.NewLevelRepository(),
		activityCashbackRepo: repo.NewActivityCashbackRepository(),
	}
}

// ProcessActivityTransactionCashback processes activity transaction cashback and commission distribution
// This method implements the cashback logic for user activities
func (s *ActivityCashbackService) ProcessActivityTransactionCashback(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	if affiliateTx == nil {
		return nil
	}
	global.GVA_LOG.Info("Processing activity transaction cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("status", string(affiliateTx.Status)))

	// Only process completed transactions
	if affiliateTx.Status != model.StatusCompleted {
		global.GVA_LOG.Debug("Skipping non-completed transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("status", string(affiliateTx.Status)))
		return nil
	}

	// Check if cashback already exists for this transaction
	existingCashback, err := s.activityCashbackRepo.GetActivityCashbackByTransactionID(ctx, affiliateTx.ID)
	if err == nil && existingCashback != nil {
		global.GVA_LOG.Debug("Cashback already exists for transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("cashback_id", existingCashback.ID.String()))
		return nil
	}

	user, err := s.getUserWithLevel(ctx, affiliateTx.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	solPrice, err := s.getSolPriceForTransaction(ctx, affiliateTx)
	if err != nil {
		return fmt.Errorf("failed to get SOL price: %w", err)
	}

	// Activity Fee Rate * QuoteAmount * SolPriceSnapshot.Price = feeRate
	feeRate := solPrice.Price.Mul(affiliateTx.QuoteAmount)
	cashbackAmountUSD := user.AgentLevel.MemeFeeRebate.Mul(feeRate)

	cashbackAmountSOL := decimal.Zero
	if !solPrice.Price.IsZero() {
		cashbackAmountSOL = cashbackAmountUSD.Div(solPrice.Price)
	}

	global.GVA_LOG.Info("Calculated activity cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("user_level", user.AgentLevel.Name),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("sol_price", solPrice.Price.String()),
		zap.String("fee_rate", feeRate.String()),
		zap.String("activity_fee_rate", user.AgentLevel.MemeFeeRate.String()),
		zap.String("activity_fee_rebate", user.AgentLevel.MemeFeeRebate.String()),
		zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
		zap.String("cashback_amount_sol", cashbackAmountSOL.String()))

	cashback := &model.ActivityCashback{
		UserID:                 affiliateTx.UserID,
		UserAddress:            affiliateTx.UserAddress,
		Status:                 "PENDING_CLAIM",
		AffiliateTransactionID: affiliateTx.ID,
		SolPriceUSD:            solPrice.Price,
		CashbackAmountUSD:      cashbackAmountUSD,
		CashbackAmountSOL:      cashbackAmountSOL,
		CreatedAt:              &time.Time{},
	}

	now := time.Now()
	cashback.CreatedAt = &now

	if err := s.activityCashbackRepo.CreateActivityCashback(ctx, cashback); err != nil {
		return fmt.Errorf("failed to create activity cashback record: %w", err)
	}

	global.GVA_LOG.Info("Activity transaction cashback processed successfully",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("cashback_id", cashback.ID.String()),
		zap.String("total_cashback_usd", cashbackAmountUSD.String()),
		zap.String("total_cashback_sol", cashbackAmountSOL.String()))

	return nil
}

func (s *ActivityCashbackService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// getSolPriceForTransaction gets the SOL price for the transaction
// We try to get the price at the transaction time, or the latest available price
func (s *ActivityCashbackService) getSolPriceForTransaction(ctx context.Context, affiliateTx *model.AffiliateTransaction) (*model.SolPriceSnapshot, error) {
	// If no price found at transaction time, get the latest available price
	latestPrice, err := s.affiliateRepo.GetLatestSolPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest SOL price: %w", err)
	}

	global.GVA_LOG.Warn("Using latest SOL price instead of transaction time price",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("latest_price", latestPrice.Price.String()),
		zap.Time("latest_price_timestamp", latestPrice.Timestamp),
		zap.Time("tx_timestamp", affiliateTx.CreatedAt))

	return latestPrice, nil
}
