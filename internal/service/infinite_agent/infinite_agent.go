package infinite_agent

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/infinite_agent"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/infinite"
	"go.uber.org/zap"
)

type InfiniteAgentConfigI interface {
	Create(ctx context.Context, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error)
	Update(ctx context.Context, id uuid.UUID, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error)
	Delete(ctx context.Context, id uuid.UUID) error
	GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error)
	GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error)
}

type InfiniteAgentConfigService struct {
	repo infinite_agent.InfiniteAgentConfigI
}

func NewInfiniteAgentConfigService(repo infinite_agent.InfiniteAgentConfigI) InfiniteAgentConfigI {
	return &InfiniteAgentConfigService{
		repo: repo,
	}
}

func (s *InfiniteAgentConfigService) Create(ctx context.Context, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error) {
	existingConfig, err := s.repo.GetByUserID(ctx, userID)
	if err == nil && existingConfig != nil {
		return nil, fmt.Errorf("infinite agent config already exists for user %s", userID)
	}

	rootUserID, err := s.findRootUserInReferralTree(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Unable to find the root user in the referral tree",
			zap.String("user_id", userID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to find root user in referral tree: %w", err)
	}

	// Check if the tree already has an infinite agent
	hasInfiniteAgent, existingInfiniteAgentID, err := s.checkTreeHasInfiniteAgent(ctx, rootUserID)
	if err != nil {
		global.GVA_LOG.Error("Failed to check if tree has infinite agent",
			zap.String("root_user_id", rootUserID.String()),
			zap.Error(err))
		return nil, fmt.Errorf("failed to check tree infinite agent status: %w", err)
	}

	if hasInfiniteAgent {
		return nil, fmt.Errorf("tree already has an infinite agent (user ID: %s), cannot create another one", existingInfiniteAgentID)
	}

	err = s.createReferralTreeSnapshot(ctx, rootUserID, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to create referral tree snapshot",
			zap.String("root_user_id", rootUserID.String()),
			zap.String("infinite_agent_user_id", userID.String()),
			zap.Error(err))
		// Don't return error here, as the snapshot creation is not critical for the main operation
	}

	config := &model.InfiniteAgentConfig{
		UserID:          userID,
		CommissionRateN: commissionRateN,
		Status:          status,
	}

	err = s.repo.Create(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create infinite agent config: %w", err)
	}

	return config, nil
}

func (s *InfiniteAgentConfigService) Update(ctx context.Context, id uuid.UUID, userID uuid.UUID, commissionRateN decimal.Decimal, status string) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("infinite agent config not found: %w", err)
	}

	if userID != uuid.Nil {
		config.UserID = userID
	}
	if !commissionRateN.IsZero() {
		config.CommissionRateN = commissionRateN
	}
	if status != "" {
		config.Status = status
	}

	err = s.repo.Update(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to update infinite agent config: %w", err)
	}

	return config, nil
}

func (s *InfiniteAgentConfigService) Delete(ctx context.Context, id uuid.UUID) error {
	// config, err := s.repo.GetByID(ctx, id)
	// if err != nil {
	// 	return fmt.Errorf("infinite agent config not found: %w", err)
	// }

	// // Delete related infinite agent referral trees first
	// err = global.GVA_DB.WithContext(ctx).Where("infinite_agent_user_id = ?", config.UserID).Delete(&model.InfiniteAgentReferralTree{}).Error
	// if err != nil {
	// 	return fmt.Errorf("failed to delete related infinite agent referral trees: %w", err)
	// }

	// // Delete related referral tree snapshots
	// err = global.GVA_DB.WithContext(ctx).Where("infinite_agent_user_id = ?", config.UserID).Delete(&model.ReferralTreeSnapshot{}).Error
	// if err != nil {
	// 	return fmt.Errorf("failed to delete related referral tree snapshots: %w", err)
	// }

	// Now delete the infinite agent config
	err := s.repo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete infinite agent config: %w", err)
	}

	return nil
}

func (s *InfiniteAgentConfigService) GetByID(ctx context.Context, id uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetByUserID(ctx context.Context, userID uuid.UUID) (*model.InfiniteAgentConfig, error) {
	config, err := s.repo.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get infinite agent config by user ID: %w", err)
	}
	return config, nil
}

func (s *InfiniteAgentConfigService) GetAll(ctx context.Context) ([]*model.InfiniteAgentConfig, error) {
	configs, err := s.repo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get all infinite agent configs: %w", err)
	}
	return configs, nil
}

func (s *InfiniteAgentConfigService) findRootUserInReferralTree(ctx context.Context, userID uuid.UUID) (uuid.UUID, error) {
	query := `
		WITH RECURSIVE ancestors AS (
			SELECT user_id, referrer_id, 0 as level
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, a.level + 1
			FROM referrals r
			JOIN ancestors a ON r.user_id = a.referrer_id
		)
		SELECT user_id FROM ancestors 
		WHERE referrer_id IS NULL 
		ORDER BY level DESC 
		LIMIT 1;
	`

	var rootUserID uuid.UUID
	err := global.GVA_DB.Raw(query, userID).Scan(&rootUserID).Error
	if err != nil {
		return uuid.Nil, fmt.Errorf("Failed to query the root user: %w", err)
	}

	if rootUserID == uuid.Nil {
		return uuid.Nil, fmt.Errorf("Root user not found")
	}

	return rootUserID, nil
}

// createReferralTreeSnapshot creates a referral tree snapshot
func (s *InfiniteAgentConfigService) createReferralTreeSnapshot(ctx context.Context, rootUserID, infiniteAgentUserID uuid.UUID) error {
	treeSnapshotTask := infinite.NewReferralTreeSnapshotTask()

	snapshotDate := time.Now().UTC()

	err := treeSnapshotTask.ProcessReferralTreeSnapshotWithInfiniteAgent(rootUserID, snapshotDate, &infiniteAgentUserID)
	if err != nil {
		return fmt.Errorf("failed to create referral tree snapshot: %w", err)
	}

	return nil
}

// checkTreeHasInfiniteAgent checks if the tree with the specified root user already has an infinite agent
func (s *InfiniteAgentConfigService) checkTreeHasInfiniteAgent(ctx context.Context, rootUserID uuid.UUID) (bool, string, error) {
	// Query the latest valid snapshot for this root user to check if it already has an infinite agent
	var snapshot model.ReferralTreeSnapshot
	err := global.GVA_DB.WithContext(ctx).
		Where("root_user_id = ? AND is_valid = true", rootUserID).
		Order("snapshot_date DESC").
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No snapshot found, indicating the tree doesn't have an infinite agent yet
			return false, "", nil
		}
		return false, "", fmt.Errorf("failed to query referral tree snapshot: %w", err)
	}

	// Check if the snapshot is marked as having an infinite agent
	if snapshot.HasInfiniteAgent && snapshot.InfiniteAgentUserID != nil {
		return true, snapshot.InfiniteAgentUserID.String(), nil
	}

	// If no snapshot record exists, also check if there are active infinite agent configurations in the tree
	// Use a simpler approach: check if any user in the tree has an active infinite agent config
	query := `
		WITH RECURSIVE tree_users AS (
			SELECT user_id, referrer_id
			FROM referrals
			WHERE referrer_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id
			FROM referrals r
			JOIN tree_users t ON r.referrer_id = t.user_id
		)
		SELECT iac.user_id
		FROM infinite_agent_configs iac
		WHERE iac.status = 'ACTIVE'
		AND iac.user_id IN (
			SELECT user_id FROM tree_users
			UNION SELECT ?
		)
		LIMIT 1;
	`

	var existingUserID uuid.UUID
	err = global.GVA_DB.WithContext(ctx).Raw(query, rootUserID, rootUserID).Scan(&existingUserID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// No active infinite agent configuration found
			return false, "", nil
		}
		return false, "", fmt.Errorf("failed to query infinite agent config: %w", err)
	}

	// Check if we found a valid user ID
	if existingUserID == uuid.Nil {
		return false, "", nil
	}

	// Found an active infinite agent configuration
	return true, existingUserID.String(), nil
}
