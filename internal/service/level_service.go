package service

import (
	"context"
	"fmt"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

type LevelService struct {
	levelRepo repo.LevelRepo
}

func NewLevelService() LevelI {
	return &LevelService{
		levelRepo: repo.NewLevelRepository(),
	}
}

// GetAgentLevels retrieves all agent levels
func (s *LevelService) GetAgentLevels(ctx context.Context) ([]model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevels(ctx)
}

// GetAgentLevelByID retrieves a specific agent level by ID
func (s *LevelService) GetAgentLevelByID(ctx context.Context, id uint) (*model.AgentLevel, error) {
	return s.levelRepo.GetAgentLevelByID(ctx, id)
}

// UpdateLevelCommission updates commission rates and meme fee rebate for a specific level
func (s *LevelService) UpdateLevelCommission(ctx context.Context, levelID uint, directRate, indirectRate, extendedRate, memeFeeRebate float64) (*model.AgentLevel, error) {
	// Validate input parameters
	if directRate < 0 || directRate > 1 {
		return nil, fmt.Errorf("direct commission rate must be between 0 and 1")
	}
	if indirectRate < 0 || indirectRate > 1 {
		return nil, fmt.Errorf("indirect commission rate must be between 0 and 1")
	}
	if extendedRate < 0 || extendedRate > 1 {
		return nil, fmt.Errorf("extended commission rate must be between 0 and 1")
	}
	if memeFeeRebate < 0 || memeFeeRebate > 1 {
		return nil, fmt.Errorf("meme fee rebate must be between 0 and 1")
	}

	// Get the current level
	level, err := s.levelRepo.GetAgentLevelByID(ctx, levelID)
	if err != nil {
		return nil, fmt.Errorf("failed to get agent level: %w", err)
	}

	// Update the commission rates and meme fee rebate
	level.DirectCommissionRate = decimal.NewFromFloat(directRate)
	level.IndirectCommissionRate = decimal.NewFromFloat(indirectRate)
	level.ExtendedCommissionRate = decimal.NewFromFloat(extendedRate)
	level.MemeFeeRebate = decimal.NewFromFloat(memeFeeRebate)

	// Save the updated level
	err = s.levelRepo.UpdateAgentLevel(ctx, level)
	if err != nil {
		return nil, fmt.Errorf("failed to update agent level: %w", err)
	}

	return level, nil
}
