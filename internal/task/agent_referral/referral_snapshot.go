package agent_referral

import (
	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SnapshotTask struct {
	repo repo.InvitationRepo
}

func NewSnapshotTask(repo repo.InvitationRepo) *SnapshotTask {
	return &SnapshotTask{
		repo: repo,
	}
}

// UpdateAllReferralSnapshots updates agent referral snapshot statistics for all users
// This includes direct count, total downline count, total volume USD, total rewards distributed, and upline relationships
func (s *SnapshotTask) UpdateAllReferralSnapshots() {
	global.GVA_LOG.Info("Starting agent referral snapshot update for all users")

	var userIDs []uuid.UUID
	global.GVA_DB.Model(&model.User{}).Pluck("id", &userIDs)

	global.GVA_LOG.Info("Found users to process", zap.Int("user_count", len(userIDs)))

	processedCount := 0
	errorCount := 0

	for _, userID := range userIDs {
		if err := s.updateUserReferralSnapshot(userID); err != nil {
			global.GVA_LOG.Error("Failed to update referral snapshot for user",
				zap.String("user_id", userID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Referral snapshot update completed",
		zap.Int("total_users", len(userIDs)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// updateUserReferralSnapshot updates referral snapshot for a single user
func (s *SnapshotTask) updateUserReferralSnapshot(userID uuid.UUID) error {
	// global.GVA_LOG.Debug("Processing referral snapshot for user", zap.String("user_id", userID.String()))

	// Calculate direct referral count (depth = 1)
	var directCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Count(&directCount)

	// Calculate total downline count (depth <= 3)
	var totalDownlineCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth <= 3", userID).
		Count(&totalDownlineCount)

	// Calculate total volume USD from affiliate transactions
	var totalVolumeUSD decimal.Decimal
	err := global.GVA_DB.Model(&model.AffiliateTransaction{}).
		Joins("JOIN referrals ON affiliate_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND affiliate_transactions.status = ?", userID, model.StatusCompleted).
		Select("COALESCE(SUM(affiliate_transactions.volume_usd), 0)").
		Scan(&totalVolumeUSD).Error
	if err != nil {
		return err
	}

	// Calculate total rewards distributed (commission amount)
	var totalRewardsDistributed decimal.Decimal
	err = global.GVA_DB.Model(&model.AffiliateTransaction{}).
		Where("referrer_id = ? AND commission_paid = ?", userID, true).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&totalRewardsDistributed).Error
	if err != nil {
		return err
	}

	// Get upline relationships (L1, L2, L3)
	var l1UplineID, l2UplineID, l3UplineID *uuid.UUID
	var referral model.Referral
	err = global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err == nil && referral.ReferrerID != nil {
		// L1 upline is the direct referrer
		l1UplineID = referral.ReferrerID

		// Find L2 upline (referrer of L1)
		var l1Referral model.Referral
		err = global.GVA_DB.Where("user_id = ?", *l1UplineID).First(&l1Referral).Error
		if err == nil && l1Referral.ReferrerID != nil {
			l2UplineID = l1Referral.ReferrerID

			// Find L3 upline (referrer of L2)
			var l2Referral model.Referral
			err = global.GVA_DB.Where("user_id = ?", *l2UplineID).First(&l2Referral).Error
			if err == nil && l2Referral.ReferrerID != nil {
				l3UplineID = l2Referral.ReferrerID
			}
		}
	}

	// Create or update ReferralSnapshot
	var snapshot model.ReferralSnapshot
	err = global.GVA_DB.Where("user_id = ?", userID).First(&snapshot).Error
	if err == gorm.ErrRecordNotFound {
		// Create new snapshot
		snapshot = model.ReferralSnapshot{
			UserID:                  userID,
			DirectCount:             int(directCount),
			TotalDownlineCount:      int(totalDownlineCount),
			TotalVolumeUSD:          totalVolumeUSD,
			TotalRewardsDistributed: totalRewardsDistributed,
			L1UplineID:              l1UplineID,
			L2UplineID:              l2UplineID,
			L3UplineID:              l3UplineID,
		}
		err = global.GVA_DB.Create(&snapshot).Error
		if err != nil {
			return err
		}
		// global.GVA_LOG.Debug("Created new referral snapshot for user",
		// 	zap.String("user_id", userID.String()),
		// 	zap.Int("direct_count", int(directCount)),
		// 	zap.Int("total_downline_count", int(totalDownlineCount)),
		// 	zap.String("total_volume_usd", totalVolumeUSD.String()),
		// 	zap.String("total_rewards_distributed", totalRewardsDistributed.String()))
		return nil
	} else if err == nil {
		// Update existing snapshot
		updates := model.ReferralSnapshot{
			DirectCount:             int(directCount),
			TotalDownlineCount:      int(totalDownlineCount),
			TotalVolumeUSD:          totalVolumeUSD,
			TotalRewardsDistributed: totalRewardsDistributed,
			L1UplineID:              l1UplineID,
			L2UplineID:              l2UplineID,
			L3UplineID:              l3UplineID,
		}
		err = global.GVA_DB.Model(&snapshot).Updates(updates).Error
		if err != nil {
			return err
		}
		// global.GVA_LOG.Debug("Updated referral snapshot for user",
		// 	zap.String("user_id", userID.String()),
		// 	zap.Int("direct_count", int(directCount)),
		// 	zap.Int("total_downline_count", int(totalDownlineCount)),
		// 	zap.String("total_volume_usd", totalVolumeUSD.String()),
		// 	zap.String("total_rewards_distributed", totalRewardsDistributed.String()))
	} else {
		return err
	}

	return nil
}
