package infinite

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// InfiniteAgentReferralTreeTask Infinite agent referral tree task
type InfiniteAgentReferralTreeTask struct{}

// NewInfiniteAgentReferralTreeTask Create new infinite agent referral tree task instance
func NewInfiniteAgentReferralTreeTask() *InfiniteAgentReferralTreeTask {
	return &InfiniteAgentReferralTreeTask{}
}

// CreateInfiniteAgentReferralTrees Create infinite agent referral trees
// Execute daily at midnight, create referral trees for each infinite agent
func (t *InfiniteAgentReferralTreeTask) CreateInfiniteAgentReferralTrees() {
	global.GVA_LOG.Info("Starting infinite agent referral tree task")

	// Get yesterday's date as snapshot date
	snapshotDate := time.Now().UTC().AddDate(0, 0, -1)
	snapshotDateStr := snapshotDate.Format("2006-01-02")

	global.GVA_LOG.Info("Snapshot date", zap.String("date", snapshotDateStr))

	// Get all active infinite agents
	infiniteAgents, err := t.getActiveInfiniteAgents()
	if err != nil {
		global.GVA_LOG.Error("Failed to get infinite agents", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found infinite agents", zap.Int("infinite_agent_count", len(infiniteAgents)))

	processedCount := 0
	errorCount := 0

	for _, infiniteAgent := range infiniteAgents {
		if err := t.processInfiniteAgentReferralTree(infiniteAgent, snapshotDate); err != nil {
			global.GVA_LOG.Error("Failed to process infinite agent referral tree",
				zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
				zap.Error(err))
			// Add debug information
			t.DebugInfiniteAgentReferralData(infiniteAgent.UserID)
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Infinite agent referral tree task completed",
		zap.String("date", snapshotDateStr),
		zap.Int("total_infinite_agents", len(infiniteAgents)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// getActiveInfiniteAgents Get all active infinite agents
func (t *InfiniteAgentReferralTreeTask) getActiveInfiniteAgents() ([]model.InfiniteAgentConfig, error) {
	var infiniteAgents []model.InfiniteAgentConfig

	err := global.GVA_DB.Where("status = ?", "ACTIVE").Find(&infiniteAgents).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query active infinite agents: %w", err)
	}

	return infiniteAgents, nil
}

// processInfiniteAgentReferralTree Process single infinite agent referral tree
func (t *InfiniteAgentReferralTreeTask) processInfiniteAgentReferralTree(infiniteAgent model.InfiniteAgentConfig, snapshotDate time.Time) error {
	global.GVA_LOG.Debug("Starting to process infinite agent referral tree",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.Time("snapshot_date", snapshotDate))

	// Check if snapshot for this date already exists
	existingTree, err := t.getExistingInfiniteAgentTree(infiniteAgent.UserID, snapshotDate)
	if err != nil {
		return fmt.Errorf("failed to check existing infinite agent tree: %w", err)
	}

	if existingTree != nil {
		global.GVA_LOG.Debug("Infinite agent tree already exists, skipping processing",
			zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
			zap.Time("snapshot_date", snapshotDate))
		return nil
	}

	// Get root user of infinite agent in referral tree
	rootUserID, err := t.FindRootUserForInfiniteAgent(infiniteAgent.UserID)
	if err != nil {
		return fmt.Errorf("failed to find root user for infinite agent: %w", err)
	}

	global.GVA_LOG.Debug("Found root user",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.String("root_user_id", rootUserID.String()))

	// Get all users in infinite agent referral tree
	treeUsers, err := t.getAllUsersInInfiniteAgentTree(infiniteAgent.UserID, rootUserID)
	if err != nil {
		return fmt.Errorf("failed to get users in infinite agent tree: %w", err)
	}

	global.GVA_LOG.Debug("Retrieved users in tree",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.Int("user_count", len(treeUsers)))

	// Calculate tree structure information and commission statistics
	treeInfo, err := t.calculateInfiniteAgentTreeInfo(infiniteAgent.UserID, rootUserID, treeUsers)
	if err != nil {
		return fmt.Errorf("failed to calculate infinite agent tree info: %w", err)
	}

	// Create infinite agent referral tree
	infiniteAgentTree := &model.InfiniteAgentReferralTree{
		InfiniteAgentUserID:   infiniteAgent.UserID,
		CommissionRateN:       infiniteAgent.CommissionRateN,
		RootUserID:            rootUserID,
		SnapshotDate:          snapshotDate,
		TotalNodes:            treeInfo.TotalNodes,
		MaxDepth:              treeInfo.MaxDepth,
		DirectCount:           treeInfo.DirectCount,
		ActiveUsers:           treeInfo.ActiveUsers,
		TradingUsers:          treeInfo.TradingUsers,
		TotalCommissionEarned: treeInfo.TotalCommissionEarned,
		TotalVolumeUSD:        treeInfo.TotalVolumeUSD,
		Status:                "ACTIVE",
		Description:           fmt.Sprintf("Infinite Agent Referral Tree - Infinite Agent: %s, Root User: %s, Date: %s", infiniteAgent.UserID.String(), rootUserID.String(), snapshotDate.Format("2006-01-02")),
	}

	// Save infinite agent referral tree
	err = global.GVA_DB.Create(infiniteAgentTree).Error
	if err != nil {
		return fmt.Errorf("failed to save infinite agent referral tree: %w", err)
	}

	// Create infinite agent tree nodes
	err = t.createInfiniteAgentTreeNodes(infiniteAgentTree.ID, treeUsers, infiniteAgent.UserID)
	if err != nil {
		return fmt.Errorf("failed to create infinite agent tree nodes: %w", err)
	}

	global.GVA_LOG.Debug("Infinite agent referral tree processing completed",
		zap.String("infinite_agent_user_id", infiniteAgent.UserID.String()),
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate),
		zap.Int("total_nodes", treeInfo.TotalNodes),
		zap.Int("max_depth", treeInfo.MaxDepth),
		zap.Int("direct_count", treeInfo.DirectCount),
		zap.String("total_commission", treeInfo.TotalCommissionEarned.String()))

	return nil
}

// getExistingInfiniteAgentTree Check if infinite agent tree already exists
func (t *InfiniteAgentReferralTreeTask) getExistingInfiniteAgentTree(infiniteAgentUserID uuid.UUID, snapshotDate time.Time) (*model.InfiniteAgentReferralTree, error) {
	var tree model.InfiniteAgentReferralTree

	err := global.GVA_DB.Where("infinite_agent_user_id = ? AND DATE(snapshot_date) = DATE(?)", infiniteAgentUserID, snapshotDate).
		First(&tree).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &tree, nil
}

// FindRootUserForInfiniteAgent Find root user of infinite agent in referral tree
func (t *InfiniteAgentReferralTreeTask) FindRootUserForInfiniteAgent(infiniteAgentUserID uuid.UUID) (uuid.UUID, error) {
	// First check if infinite agent user has referrer
	var referral model.Referral
	err := global.GVA_DB.Where("user_id = ?", infiniteAgentUserID).First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// If infinite agent user has no referrer, then it is the root user
			global.GVA_LOG.Debug("Infinite agent user has no referrer, treating as root user",
				zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))
			return infiniteAgentUserID, nil
		}
		return uuid.Nil, fmt.Errorf("failed to query infinite agent user referral relationship: %w", err)
	}

	// If infinite agent user has referrer, use recursive CTE query to find root user
	query := `
		WITH RECURSIVE ancestors AS (
			SELECT user_id, referrer_id, 0 as level
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, a.level + 1
			FROM referrals r
			JOIN ancestors a ON r.user_id = a.referrer_id
			WHERE r.referrer_id IS NOT NULL
		)
		SELECT user_id FROM ancestors 
		WHERE referrer_id IS NULL 
		ORDER BY level DESC 
		LIMIT 1;
	`

	var rootUserID uuid.UUID
	err = global.GVA_DB.Raw(query, infiniteAgentUserID).Scan(&rootUserID).Error
	if err != nil {
		return uuid.Nil, fmt.Errorf("failed to query root user: %w", err)
	}

	if rootUserID == uuid.Nil {
		// If recursive query doesn't find root user, it might be a data issue, log warning and return infinite agent user as root user
		global.GVA_LOG.Warn("Recursive query didn't find root user, using infinite agent user as root user",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))
		return infiniteAgentUserID, nil
	}

	return rootUserID, nil
}

// getAllUsersInInfiniteAgentTree Get all users in infinite agent referral tree
func (t *InfiniteAgentReferralTreeTask) getAllUsersInInfiniteAgentTree(infiniteAgentUserID, rootUserID uuid.UUID) ([]model.User, error) {
	var users []model.User

	// If root user is the infinite agent user, need to get infinite agent user and all users below
	if rootUserID == infiniteAgentUserID {
		query := `
			WITH RECURSIVE descendants AS (
				SELECT id, email, invitation_code, created_at, updated_at, deleted_at, 
					   agent_level_id, level_grace_period_started_at, level_upgraded_at, 
					   first_transaction_at, 0 as depth, NULL::uuid as parent_id, NULL::uuid as referrer_id
				FROM users WHERE id = ?
				UNION ALL
				SELECT u.id, u.email, u.invitation_code, u.created_at, u.updated_at, u.deleted_at,
					   u.agent_level_id, u.level_grace_period_started_at, u.level_upgraded_at,
					   u.first_transaction_at, d.depth + 1, d.id, r.referrer_id
				FROM users u
				JOIN referrals r ON u.id = r.user_id
				JOIN descendants d ON d.id = r.referrer_id
			)
			SELECT * FROM descendants ORDER BY depth, id;
		`

		err := global.GVA_DB.Raw(query, infiniteAgentUserID).Scan(&users).Error
		if err != nil {
			return nil, fmt.Errorf("failed to recursively query infinite agent user and descendants: %w", err)
		}

		global.GVA_LOG.Debug("Retrieved infinite agent user and descendants",
			zap.String("infinite_agent_user_id", infiniteAgentUserID.String()),
			zap.Int("user_count", len(users)))
	} else {
		// If root user is not infinite agent user, get all descendants starting from root user
		query := `
			WITH RECURSIVE descendants AS (
				SELECT id, email, invitation_code, created_at, updated_at, deleted_at, 
					   agent_level_id, level_grace_period_started_at, level_upgraded_at, 
					   first_transaction_at, 0 as depth, NULL::uuid as parent_id, NULL::uuid as referrer_id
				FROM users WHERE id = ?
				UNION ALL
				SELECT u.id, u.email, u.invitation_code, u.created_at, u.updated_at, u.deleted_at,
					   u.agent_level_id, u.level_grace_period_started_at, u.level_upgraded_at,
					   u.first_transaction_at, d.depth + 1, d.id, r.referrer_id
				FROM users u
				JOIN referrals r ON u.id = r.user_id
				JOIN descendants d ON d.id = r.referrer_id
			)
			SELECT * FROM descendants ORDER BY depth, id;
		`

		err := global.GVA_DB.Raw(query, rootUserID).Scan(&users).Error
		if err != nil {
			return nil, fmt.Errorf("failed to recursively query users: %w", err)
		}

		global.GVA_LOG.Debug("Retrieved root user and descendants",
			zap.String("root_user_id", rootUserID.String()),
			zap.Int("user_count", len(users)))
	}

	return users, nil
}

// InfiniteAgentTreeInfo Infinite agent tree information structure
type InfiniteAgentTreeInfo struct {
	TotalNodes            int
	MaxDepth              int
	DirectCount           int
	ActiveUsers           int
	TradingUsers          int
	TotalCommissionEarned decimal.Decimal
	TotalVolumeUSD        decimal.Decimal
}

// calculateInfiniteAgentTreeInfo Calculate infinite agent tree structure information and commission statistics
func (t *InfiniteAgentReferralTreeTask) calculateInfiniteAgentTreeInfo(infiniteAgentUserID, rootUserID uuid.UUID, users []model.User) (*InfiniteAgentTreeInfo, error) {
	if len(users) == 0 {
		return &InfiniteAgentTreeInfo{}, nil
	}

	info := &InfiniteAgentTreeInfo{
		TotalNodes:            len(users),
		TotalCommissionEarned: decimal.Zero,
		TotalVolumeUSD:        decimal.Zero,
	}

	// Calculate max depth and direct referral count
	maxDepth := 0
	directCount := 0
	activeUsers := 0
	tradingUsers := 0

	for _, user := range users {
		// Calculate depth
		depth, err := t.calculateUserDepth(rootUserID, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		if depth == 1 {
			directCount++
		}

		if depth > maxDepth {
			maxDepth = depth
		}

		// Count active users
		if user.FirstTransactionAt != nil {
			activeUsers++
			tradingUsers++
		}

		// Calculate user commission and trading volume (need to implement based on actual business logic)
		userCommission, userVolume, err := t.calculateUserCommissionAndVolume(user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user commission and volume", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		info.TotalCommissionEarned = info.TotalCommissionEarned.Add(userCommission)
		info.TotalVolumeUSD = info.TotalVolumeUSD.Add(userVolume)
	}

	info.MaxDepth = maxDepth
	info.DirectCount = directCount
	info.ActiveUsers = activeUsers
	info.TradingUsers = tradingUsers

	return info, nil
}

// calculateUserDepth Calculate user depth in tree
func (t *InfiniteAgentReferralTreeTask) calculateUserDepth(rootUserID, userID uuid.UUID) (int, error) {
	if rootUserID == userID {
		return 0, nil
	}

	var depth int
	query := `
		WITH RECURSIVE path AS (
			SELECT user_id, referrer_id, 1 as depth
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, p.depth + 1
			FROM referrals r
			JOIN path p ON r.user_id = p.referrer_id
			WHERE p.referrer_id != ?
		)
		SELECT MAX(depth) FROM path;
	`

	err := global.GVA_DB.Raw(query, userID, rootUserID).Scan(&depth).Error
	if err != nil {
		return 0, err
	}

	return depth, nil
}

// calculateUserCommissionAndVolume Calculate user commission and trading volume
func (t *InfiniteAgentReferralTreeTask) calculateUserCommissionAndVolume(userID uuid.UUID) (decimal.Decimal, decimal.Decimal, error) {
	// This needs to be implemented based on actual commission calculation logic
	// Temporarily return zero values, actual implementation needs to query related transaction and commission records
	return decimal.Zero, decimal.Zero, nil
}

// createInfiniteAgentTreeNodes Create infinite agent tree nodes
func (t *InfiniteAgentReferralTreeTask) createInfiniteAgentTreeNodes(treeID uint, users []model.User, infiniteAgentUserID uuid.UUID) error {
	var treeNodes []model.InfiniteAgentTreeNode

	for i, user := range users {
		// Calculate user depth
		depth, err := t.calculateUserDepth(users[0].ID, user.ID) // First user is root user
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		// Get referrer ID
		var referrerID *uuid.UUID
		if depth > 0 {
			referrerID, err = t.getReferrerID(user.ID)
			if err != nil {
				global.GVA_LOG.Warn("Failed to get referrer ID", zap.String("user_id", user.ID.String()), zap.Error(err))
			}
		}

		// Determine if trading user
		isTrading := user.FirstTransactionAt != nil

		// Calculate user commission and trading volume
		userCommission, userVolume, err := t.calculateUserCommissionAndVolume(user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user commission and volume", zap.String("user_id", user.ID.String()), zap.Error(err))
			userCommission = decimal.Zero
			userVolume = decimal.Zero
		}

		treeNode := model.InfiniteAgentTreeNode{
			TreeID:           treeID,
			UserID:           user.ID,
			ParentUserID:     referrerID,
			ReferrerID:       referrerID,
			Depth:            depth,
			Level:            depth + 1, // Level starts from 1
			Position:         i + 1,     // Position starts from 1
			IsActive:         true,
			IsTrading:        isTrading,
			AgentLevelID:     user.AgentLevelID,
			CommissionEarned: userCommission,
			VolumeUSD:        userVolume,
		}

		treeNodes = append(treeNodes, treeNode)
	}

	if len(treeNodes) > 0 {
		err := global.GVA_DB.CreateInBatches(treeNodes, 100).Error
		if err != nil {
			return fmt.Errorf("failed to batch create infinite agent tree nodes: %w", err)
		}
	}

	return nil
}

// getReferrerID Get user's referrer ID
func (t *InfiniteAgentReferralTreeTask) getReferrerID(userID uuid.UUID) (*uuid.UUID, error) {
	var referral model.Referral

	err := global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return referral.ReferrerID, nil
}

// DebugInfiniteAgentReferralData Debug method: check infinite agent referral relationship data
func (t *InfiniteAgentReferralTreeTask) DebugInfiniteAgentReferralData(infiniteAgentUserID uuid.UUID) {
	global.GVA_LOG.Info("Starting to debug infinite agent referral relationship data",
		zap.String("infinite_agent_user_id", infiniteAgentUserID.String()))

	// Check if infinite agent user exists
	var user model.User
	err := global.GVA_DB.Where("id = ?", infiniteAgentUserID).First(&user).Error
	if err != nil {
		global.GVA_LOG.Error("Infinite agent user doesn't exist", zap.Error(err))
		return
	}
	global.GVA_LOG.Info("Infinite agent user exists", zap.String("email", *user.Email))

	// Check infinite agent configuration
	var config model.InfiniteAgentConfig
	err = global.GVA_DB.Where("user_id = ?", infiniteAgentUserID).First(&config).Error
	if err != nil {
		global.GVA_LOG.Error("Infinite agent configuration doesn't exist", zap.Error(err))
		return
	}
	global.GVA_LOG.Info("Infinite agent configuration exists", zap.String("status", config.Status))

	// Check referral relationship
	var referral model.Referral
	err = global.GVA_DB.Where("user_id = ?", infiniteAgentUserID).First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			global.GVA_LOG.Info("Infinite agent user has no referrer (is root user)")
		} else {
			global.GVA_LOG.Error("Failed to query referral relationship", zap.Error(err))
		}
		return
	}
	global.GVA_LOG.Info("Infinite agent user has referrer",
		zap.String("referrer_id", referral.ReferrerID.String()))

	// Recursively query all ancestors
	query := `
		WITH RECURSIVE ancestors AS (
			SELECT user_id, referrer_id, 0 as level
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, a.level + 1
			FROM referrals r
			JOIN ancestors a ON r.user_id = a.referrer_id
			WHERE r.referrer_id IS NOT NULL
		)
		SELECT user_id, referrer_id, level FROM ancestors ORDER BY level;
	`

	type AncestorResult struct {
		UserID     uuid.UUID  `json:"user_id"`
		ReferrerID *uuid.UUID `json:"referrer_id"`
		Level      int        `json:"level"`
	}

	var ancestors []AncestorResult
	err = global.GVA_DB.Raw(query, infiniteAgentUserID).Scan(&ancestors).Error
	if err != nil {
		global.GVA_LOG.Error("Failed to query ancestors", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Ancestor chain",
		zap.Int("ancestor_count", len(ancestors)),
		zap.Any("ancestors", ancestors))

	// Find root user
	var rootUserID uuid.UUID
	for _, ancestor := range ancestors {
		if ancestor.ReferrerID == nil {
			rootUserID = ancestor.UserID
			break
		}
	}

	if rootUserID == uuid.Nil {
		global.GVA_LOG.Warn("Root user not found, using infinite agent user as root user")
		rootUserID = infiniteAgentUserID
	} else {
		global.GVA_LOG.Info("Found root user", zap.String("root_user_id", rootUserID.String()))
	}

	// Query root user information
	var rootUser model.User
	err = global.GVA_DB.Where("id = ?", rootUserID).First(&rootUser).Error
	if err != nil {
		global.GVA_LOG.Error("Failed to query root user information", zap.Error(err))
		return
	}
	global.GVA_LOG.Info("Root user information", zap.String("email", *rootUser.Email))
}
