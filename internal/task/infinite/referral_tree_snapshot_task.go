package infinite

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// ReferralTreeSnapshotTask Referral tree snapshot task
type ReferralTreeSnapshotTask struct{}

// NewReferralTreeSnapshotTask Create new referral tree snapshot task instance
func NewReferralTreeSnapshotTask() *ReferralTreeSnapshotTask {
	return &ReferralTreeSnapshotTask{}
}

// CreateReferralTreeSnapshots Create referral tree snapshots
// Execute daily at midnight, create referral tree snapshots for each root user
func (t *ReferralTreeSnapshotTask) CreateReferralTreeSnapshots() {
	global.GVA_LOG.Info("Starting referral tree snapshot task")

	// Get yesterday's date as snapshot date
	// snapshotDate := time.Now().UTC().AddDate(0, 0, -1)
	snapshotDate := time.Now().UTC()
	snapshotDateStr := snapshotDate.Format("2006-01-02")

	global.GVA_LOG.Info("Snapshot date", zap.String("date", snapshotDateStr))

	// Get all root users (users without referrers)
	rootUsers, err := t.getRootUsers()
	if err != nil {
		global.GVA_LOG.Error("Failed to get root users", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found root users", zap.Int("root_user_count", len(rootUsers)))

	processedCount := 0
	errorCount := 0

	for _, rootUser := range rootUsers {
		if err := t.processReferralTreeSnapshot(rootUser.ID, snapshotDate); err != nil {
			global.GVA_LOG.Error("Failed to process referral tree snapshot",
				zap.String("root_user_id", rootUser.ID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Referral tree snapshot task completed",
		zap.String("date", snapshotDateStr),
		zap.Int("total_trees", len(rootUsers)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// getRootUsers Get all root users (users without referrers)
func (t *ReferralTreeSnapshotTask) getRootUsers() ([]model.User, error) {
	var rootUsers []model.User

	err := global.GVA_DB.Debug().
		Joins("LEFT JOIN referrals ON users.id = referrals.user_id").
		Where("referrals.user_id IS NULL").
		Find(&rootUsers).Error

	if err != nil {
		return nil, fmt.Errorf("failed to query root users: %w", err)
	}

	return rootUsers, nil
}

// processReferralTreeSnapshot Process single referral tree snapshot
func (t *ReferralTreeSnapshotTask) processReferralTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) error {
	return t.ProcessReferralTreeSnapshotWithInfiniteAgent(rootUserID, snapshotDate, nil)
}

// ProcessReferralTreeSnapshotWithInfiniteAgent Process single referral tree snapshot (with infinite agent information)
func (t *ReferralTreeSnapshotTask) ProcessReferralTreeSnapshotWithInfiniteAgent(rootUserID uuid.UUID, snapshotDate time.Time, infiniteAgentUserID *uuid.UUID) error {
	// Check if snapshot for this date already exists
	existingSnapshot, err := t.getExistingSnapshot(rootUserID, snapshotDate)
	if err != nil {
		return fmt.Errorf("failed to check existing snapshot: %w", err)
	}

	if existingSnapshot != nil {
		global.GVA_LOG.Debug("Snapshot already exists, skipping processing",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate))
		return nil
	}

	// Get all users in tree
	treeUsers, err := t.getAllUsersInTree(rootUserID)
	if err != nil {
		return fmt.Errorf("failed to get users in tree: %w", err)
	}

	// Calculate tree structure information
	treeInfo, err := t.calculateTreeInfo(rootUserID, treeUsers)
	if err != nil {
		return fmt.Errorf("failed to calculate tree info: %w", err)
	}

	// Check if contains infinite agent
	hasInfiniteAgent := infiniteAgentUserID != nil
	if !hasInfiniteAgent {
		// If no infinite agent specified, check if tree contains infinite agent
		hasInfiniteAgent, infiniteAgentUserID = t.checkInfiniteAgentInTree(treeUsers)
	}

	// Create tree snapshot
	treeSnapshot := &model.ReferralTreeSnapshot{
		RootUserID:          rootUserID,
		SnapshotDate:        snapshotDate,
		TotalNodes:          treeInfo.TotalNodes,
		MaxDepth:            treeInfo.MaxDepth,
		DirectCount:         treeInfo.DirectCount,
		ActiveUsers:         treeInfo.ActiveUsers,
		TradingUsers:        treeInfo.TradingUsers,
		InfiniteAgentUserID: infiniteAgentUserID,
		HasInfiniteAgent:    hasInfiniteAgent,
		Description:         fmt.Sprintf("Referral Tree Snapshot - Root User: %s, Date: %s", rootUserID.String(), snapshotDate.Format("2006-01-02")),
		IsValid:             true,
	}

	// Save tree snapshot
	err = global.GVA_DB.Create(treeSnapshot).Error
	if err != nil {
		return fmt.Errorf("failed to save tree snapshot: %w", err)
	}

	// Create tree node snapshots
	err = t.createTreeNodes(treeSnapshot.ID, treeUsers)
	if err != nil {
		return fmt.Errorf("failed to create tree node snapshots: %w", err)
	}

	global.GVA_LOG.Debug("Referral tree snapshot processing completed",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate),
		zap.Int("total_nodes", treeInfo.TotalNodes),
		zap.Int("max_depth", treeInfo.MaxDepth),
		zap.Int("direct_count", treeInfo.DirectCount),
		zap.Bool("has_infinite_agent", hasInfiniteAgent))

	return nil
}

// getExistingSnapshot Check if snapshot already exists
func (t *ReferralTreeSnapshotTask) getExistingSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) (*model.ReferralTreeSnapshot, error) {
	var snapshot model.ReferralTreeSnapshot

	err := global.GVA_DB.Where("root_user_id = ? AND DATE(snapshot_date) = DATE(?)", rootUserID, snapshotDate).
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &snapshot, nil
}

// getAllUsersInTree Get all users in tree
func (t *ReferralTreeSnapshotTask) getAllUsersInTree(rootUserID uuid.UUID) ([]model.User, error) {
	var users []model.User

	query := `
		WITH RECURSIVE descendants AS (
			SELECT id, email, invitation_code, created_at, updated_at, deleted_at, 
				   agent_level_id, level_grace_period_started_at, level_upgraded_at, 
				   first_transaction_at, 0 as depth, NULL::uuid as parent_id, NULL::uuid as referrer_id
			FROM users WHERE id = ?
			UNION ALL
			SELECT u.id, u.email, u.invitation_code, u.created_at, u.updated_at, u.deleted_at,
				   u.agent_level_id, u.level_grace_period_started_at, u.level_upgraded_at,
				   u.first_transaction_at, d.depth + 1, d.id, r.referrer_id
			FROM users u
			JOIN referrals r ON u.id = r.user_id
			JOIN descendants d ON d.id = r.referrer_id
		)
		SELECT * FROM descendants ORDER BY depth, id;
	`

	err := global.GVA_DB.Raw(query, rootUserID).Scan(&users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to recursively query users: %w", err)
	}

	return users, nil
}

// TreeInfo Tree information structure
type TreeInfo struct {
	TotalNodes   int
	MaxDepth     int
	DirectCount  int
	ActiveUsers  int
	TradingUsers int
}

// calculateTreeInfo Calculate tree structure information
func (t *ReferralTreeSnapshotTask) calculateTreeInfo(rootUserID uuid.UUID, users []model.User) (*TreeInfo, error) {
	if len(users) == 0 {
		return &TreeInfo{}, nil
	}

	info := &TreeInfo{
		TotalNodes: len(users),
	}

	// Calculate max depth and direct referral count
	maxDepth := 0
	directCount := 0
	activeUsers := 0
	tradingUsers := 0

	for _, user := range users {
		// Calculate depth (by querying referral relationship)
		depth, err := t.calculateUserDepth(rootUserID, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		if depth == 1 {
			directCount++
		}

		if depth > maxDepth {
			maxDepth = depth
		}

		// Count active users (users with transaction records)
		if user.FirstTransactionAt != nil {
			activeUsers++
			tradingUsers++
		}
	}

	info.MaxDepth = maxDepth
	info.DirectCount = directCount
	info.ActiveUsers = activeUsers
	info.TradingUsers = tradingUsers

	return info, nil
}

// calculateUserDepth Calculate user depth in tree
func (t *ReferralTreeSnapshotTask) calculateUserDepth(rootUserID, userID uuid.UUID) (int, error) {
	if rootUserID == userID {
		return 0, nil
	}

	var depth int
	query := `
		WITH RECURSIVE path AS (
			SELECT user_id, referrer_id, 1 as depth
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, p.depth + 1
			FROM referrals r
			JOIN path p ON r.user_id = p.referrer_id
			WHERE p.referrer_id != ?
		)
		SELECT MAX(depth) FROM path;
	`

	err := global.GVA_DB.Raw(query, userID, rootUserID).Scan(&depth).Error
	if err != nil {
		return 0, err
	}

	return depth, nil
}

// createTreeNodes Create tree node snapshots
func (t *ReferralTreeSnapshotTask) createTreeNodes(treeSnapshotID uint, users []model.User) error {
	var treeNodes []model.ReferralTreeNode

	for i, user := range users {
		// Calculate user depth
		depth, err := t.calculateUserDepth(users[0].ID, user.ID) // First user is root user
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		// Get referrer ID
		var referrerID *uuid.UUID
		if depth > 0 {
			referrerID, err = t.getReferrerID(user.ID)
			if err != nil {
				global.GVA_LOG.Warn("Failed to get referrer ID", zap.String("user_id", user.ID.String()), zap.Error(err))
			}
		}

		// Determine if trading user
		isTrading := user.FirstTransactionAt != nil

		treeNode := model.ReferralTreeNode{
			TreeSnapshotID: treeSnapshotID,
			UserID:         user.ID,
			ParentUserID:   referrerID,
			ReferrerID:     referrerID,
			Depth:          depth,
			Level:          depth + 1, // Level starts from 1
			Position:       i + 1,     // Position starts from 1
			IsActive:       true,
			IsTrading:      isTrading,
			AgentLevelID:   user.AgentLevelID,
		}

		treeNodes = append(treeNodes, treeNode)
	}

	if len(treeNodes) > 0 {
		err := global.GVA_DB.CreateInBatches(treeNodes, 100).Error
		if err != nil {
			return fmt.Errorf("failed to batch create tree nodes: %w", err)
		}
	}

	return nil
}

// getReferrerID Get user's referrer ID
func (t *ReferralTreeSnapshotTask) getReferrerID(userID uuid.UUID) (*uuid.UUID, error) {
	var referral model.Referral

	err := global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return referral.ReferrerID, nil
}

// checkInfiniteAgentInTree Check if tree contains infinite agent
func (t *ReferralTreeSnapshotTask) checkInfiniteAgentInTree(users []model.User) (bool, *uuid.UUID) {
	for _, user := range users {
		// Check if user has active infinite agent configuration
		var config model.InfiniteAgentConfig
		err := global.GVA_DB.Where("user_id = ? AND status = ?", user.ID, "ACTIVE").First(&config).Error
		if err == nil {
			// Found active infinite agent configuration
			return true, &user.ID
		}
	}
	return false, nil
}
