# Unstable Environment Configuration
# This file contains environment variables for unstable deployment

# Application Configuration
APP_ENV=unstable
APP_NAME=xbit-agent
APP_VERSION=1.0.0

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Database Configuration (PostgreSQL)
POSTGRES_AGENCY_HOST=
POSTGRES_AGENCY_PORT=
POSTGRES_AGENCY_USER=
POSTGRES_AGENCY_PASS=
POSTGRES_AGENCY_SSL_MODE=disable
POSTGRES_DB=

# Database URL for migrations
DATABASE_URL=

# Redis Configuration (Optional)
REDIS_HOST=
REDIS_PORT=6379
REDIS_PASS=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=unstable-jwt-secret-key-change-in-production
JWT_EXPIRES_TIME=7d
JWT_BUFFER_TIME=1d
JWT_ISSUER=xbit-agent-unstable

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=console
LOG_DIRECTOR=log
LOG_SHOW_LINE=true
LOG_IN_CONSOLE=true

# CORS Configuration
CORS_ALLOW_ORIGINS=*
CORS_ALLOW_METHODS=GET,POST,PUT,PATCH,DELETE,HEAD,OPTIONS
CORS_ALLOW_HEADERS=Origin,Content-Length,Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_COUNT=15000
RATE_LIMIT_TIME=3600

# Unstable Settings
DEBUG=true
ENABLE_PLAYGROUND=true

SYSTEM_LAUNCH_DATE=2025-08-16